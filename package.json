{"name": "local-llm-web", "version": "1.0.0", "description": "WebGPU-based LLM agent with whiteboard interface", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@huggingface/transformers": "^2.17.2", "@webgpu/types": "^0.1.40", "reactflow": "^11.10.4", "framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.1.0", "@vitest/ui": "^1.1.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jsdom": "^23.0.1"}, "engines": {"node": ">=18.0.0"}}