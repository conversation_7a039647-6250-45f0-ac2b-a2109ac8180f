import React, { useState, useRef, useEffect } from 'react'
import { Handle, Position, NodeProps } from 'reactflow'
import { motion } from 'framer-motion'
import { Edit3, Save, X } from 'lucide-react'

interface NoteNodeData {
  title: string
  content: string
}

export default function NoteNode({ data, selected }: NodeProps<NoteNodeData>) {
  const [isEditing, setIsEditing] = useState(false)
  const [title, setTitle] = useState(data.title)
  const [content, setContent] = useState(data.content)
  const titleRef = useRef<HTMLInputElement>(null)
  const contentRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (isEditing && titleRef.current) {
      titleRef.current.focus()
    }
  }, [isEditing])

  const handleSave = () => {
    // Update the node data (in a real app, you'd update the node state)
    data.title = title
    data.content = content
    setIsEditing(false)
  }

  const handleCancel = () => {
    setTitle(data.title)
    setContent(data.content)
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel()
    } else if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    }
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-yellow-50 rounded-lg shadow-md border-2 transition-all duration-200 ${
        selected ? 'border-yellow-500 shadow-lg' : 'border-yellow-200 hover:border-yellow-400'
      }`}
      style={{ width: 240, minHeight: 140 }}
    >
      <Handle type="target" position={Position.Top} className="!bg-yellow-500" />
      
      {/* Header */}
      <div className="p-3 border-b border-yellow-200 bg-yellow-100/50">
        <div className="flex items-center justify-between">
          {isEditing ? (
            <input
              ref={titleRef}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-1 text-sm font-medium bg-transparent border-none outline-none text-gray-900"
              placeholder="Note title..."
            />
          ) : (
            <h3 className="font-medium text-gray-900 text-sm flex-1">
              {data.title}
            </h3>
          )}
          
          <div className="flex items-center space-x-1">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="p-1 text-green-600 hover:text-green-700 transition-colors"
                  title="Save (Ctrl+Enter)"
                >
                  <Save className="w-3 h-3" />
                </button>
                <button
                  onClick={handleCancel}
                  className="p-1 text-red-600 hover:text-red-700 transition-colors"
                  title="Cancel (Esc)"
                >
                  <X className="w-3 h-3" />
                </button>
              </>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                title="Edit note"
              >
                <Edit3 className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-3 flex-1">
        {isEditing ? (
          <textarea
            ref={contentRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            className="w-full h-20 text-sm bg-transparent border-none outline-none text-gray-700 resize-none"
            placeholder="Write your note here..."
          />
        ) : (
          <p className="text-sm text-gray-700 leading-relaxed whitespace-pre-wrap">
            {data.content}
          </p>
        )}
      </div>

      {/* Footer */}
      <div className="px-3 pb-3">
        <span className="inline-flex px-2 py-1 text-xs font-medium bg-yellow-200 text-yellow-800 rounded-full">
          Note
        </span>
      </div>

      <Handle type="source" position={Position.Bottom} className="!bg-yellow-500" />
    </motion.div>
  )
}
