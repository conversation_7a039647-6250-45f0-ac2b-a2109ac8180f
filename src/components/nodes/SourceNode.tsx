import React from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { motion } from 'framer-motion'
import { ExternalLink, Globe } from 'lucide-react'
import { extractDomain, truncateText } from '@/utils'

interface SourceNodeData {
  title: string
  content: string
  url?: string
  thumbnail?: string
  source?: any
}

export default function SourceNode({ data, selected }: NodeProps<SourceNodeData>) {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (data.url) {
      window.open(data.url, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-white rounded-lg shadow-md border-2 transition-all duration-200 ${
        selected ? 'border-primary-500 shadow-lg' : 'border-gray-200 hover:border-primary-300'
      }`}
      style={{ width: 280, minHeight: 160 }}
    >
      <Handle type="target" position={Position.Top} className="!bg-primary-500" />
      
      {/* <PERSON><PERSON> */}
      <div className="p-3 border-b border-gray-100">
        <div className="flex items-start space-x-2">
          {data.thumbnail ? (
            <img
              src={data.thumbnail}
              alt=""
              className="w-8 h-8 rounded object-cover flex-shrink-0"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          ) : (
            <div className="w-8 h-8 bg-primary-100 rounded flex items-center justify-center flex-shrink-0">
              <Globe className="w-4 h-4 text-primary-600" />
            </div>
          )}
          
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-gray-900 text-sm leading-tight">
              {truncateText(data.title, 60)}
            </h3>
            {data.url && (
              <div className="text-xs text-gray-500 mt-1">
                {extractDomain(data.url)}
              </div>
            )}
          </div>
          
          {data.url && (
            <button
              onClick={handleClick}
              className="p-1 text-gray-400 hover:text-primary-600 transition-colors"
              title="Open in new tab"
            >
              <ExternalLink className="w-3 h-3" />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        <p className="text-sm text-gray-600 leading-relaxed">
          {truncateText(data.content, 120)}
        </p>
      </div>

      {/* Footer */}
      <div className="px-3 pb-3">
        <div className="flex items-center justify-between">
          <span className="inline-flex px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
            Source
          </span>
          
          {data.source?.relevanceScore && (
            <div className="text-xs text-gray-500">
              {Math.round(data.source.relevanceScore * 100)}% relevant
            </div>
          )}
        </div>
      </div>

      <Handle type="source" position={Position.Bottom} className="!bg-primary-500" />
    </motion.div>
  )
}
