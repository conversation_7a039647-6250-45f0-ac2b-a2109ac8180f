import React from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow'
import { motion } from 'framer-motion'
import { MessageSquare } from 'lucide-react'
import { truncateText } from '@/utils'

interface QueryNodeData {
  title: string
  content: string
}

export default function QueryNode({ data, selected }: NodeProps<QueryNodeData>) {
  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      className={`bg-orange-50 rounded-lg shadow-md border-2 transition-all duration-200 ${
        selected ? 'border-orange-500 shadow-lg' : 'border-orange-200 hover:border-orange-400'
      }`}
      style={{ width: 220, minHeight: 120 }}
    >
      <Handle type="source" position={Position.Right} className="!bg-orange-500" />
      
      {/* Header */}
      <div className="p-3 border-b border-orange-200 bg-orange-100/50">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-orange-200 rounded flex items-center justify-center flex-shrink-0">
            <MessageSquare className="w-3 h-3 text-orange-600" />
          </div>
          <h3 className="font-medium text-gray-900 text-sm">
            {data.title}
          </h3>
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        <p className="text-sm text-gray-700 leading-relaxed">
          {truncateText(data.content, 80)}
        </p>
      </div>

      {/* Footer */}
      <div className="px-3 pb-3">
        <span className="inline-flex px-2 py-1 text-xs font-medium bg-orange-200 text-orange-800 rounded-full">
          Query
        </span>
      </div>
    </motion.div>
  )
}
