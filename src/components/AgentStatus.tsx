import React from 'react'
import { motion } from 'framer-motion'
import { Search, Globe, Brain, Loader2 } from 'lucide-react'
import type { AgentState } from '@/types'

interface AgentStatusProps {
  agentState: AgentState
}

export default function AgentStatus({ agentState }: AgentStatusProps) {
  const getToolIcon = (toolName?: string) => {
    switch (toolName) {
      case 'web_search':
        return <Search className="w-4 h-4" />
      case 'fetch_webpage':
        return <Globe className="w-4 h-4" />
      default:
        return <Brain className="w-4 h-4" />
    }
  }

  const getStatusText = () => {
    if (agentState.currentTool) {
      switch (agentState.currentTool) {
        case 'web_search':
          return 'Searching the web...'
        case 'fetch_webpage':
          return 'Fetching webpage content...'
        default:
          return `Using ${agentState.currentTool}...`
      }
    }
    
    if (agentState.isThinking) {
      return 'Thinking...'
    }
    
    return 'Processing...'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex justify-start"
    >
      <div className="flex items-start space-x-2 max-w-[80%]">
        {/* Avatar */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-200 text-gray-600 flex items-center justify-center mr-2">
          <Brain className="w-4 h-4" />
        </div>

        {/* Status Content */}
        <div className="bg-white border border-gray-200 rounded-2xl px-4 py-3">
          <div className="flex items-center space-x-2">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              {agentState.currentTool ? getToolIcon(agentState.currentTool) : <Loader2 className="w-4 h-4" />}
            </motion.div>
            
            <span className="text-sm text-gray-700">
              {getStatusText()}
            </span>
          </div>

          {/* Progress Bar */}
          {agentState.toolProgress !== undefined && (
            <div className="mt-2">
              <div className="w-full bg-gray-200 rounded-full h-1.5">
                <motion.div
                  className="bg-primary-600 h-1.5 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${agentState.toolProgress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {Math.round(agentState.toolProgress)}% complete
              </div>
            </div>
          )}

          {/* Thinking Animation */}
          {agentState.isThinking && !agentState.currentTool && (
            <div className="flex space-x-1 mt-2">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 bg-gray-400 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  )
}
