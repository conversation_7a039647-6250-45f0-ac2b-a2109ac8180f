import React from 'react'
import { motion } from 'framer-motion'
import { <PERSON>r, Bo<PERSON>, ExternalLink, Clock } from 'lucide-react'
import type { Message } from '@/types'
import { formatTimestamp, extractDomain } from '@/utils'

interface MessageBubbleProps {
  message: Message
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user'
  const isSystem = message.role === 'system'

  if (isSystem) return null // Don't render system messages

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}
    >
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-2`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser ? 'bg-primary-600 text-white ml-2' : 'bg-gray-200 text-gray-600 mr-2'
        }`}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>

        {/* Message Content */}
        <div className={`rounded-2xl px-4 py-3 ${
          isUser 
            ? 'bg-primary-600 text-white' 
            : 'bg-white border border-gray-200 text-gray-900'
        }`}>
          {/* Main Content */}
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>

          {/* Tool Calls */}
          {message.toolCalls && message.toolCalls.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-300/30">
              <div className="text-xs opacity-75 mb-2">Tools used:</div>
              <div className="space-y-1">
                {message.toolCalls.map((toolCall) => (
                  <div
                    key={toolCall.id}
                    className={`text-xs px-2 py-1 rounded ${
                      toolCall.status === 'success'
                        ? 'bg-green-100 text-green-800'
                        : toolCall.status === 'error'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {toolCall.name}
                    {toolCall.status === 'success' && toolCall.name === 'web_search' && (
                      <span className="ml-1">
                        ({Array.isArray(toolCall.result) ? toolCall.result.length : 0} results)
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Sources */}
          {message.sources && message.sources.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-300/30">
              <div className="text-xs opacity-75 mb-2">Sources:</div>
              <div className="space-y-2">
                {message.sources.map((source) => (
                  <motion.a
                    key={source.id}
                    href={source.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.02 }}
                    className={`block p-2 rounded-lg border transition-colors ${
                      isUser
                        ? 'border-white/30 hover:bg-white/10'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start space-x-2">
                      {source.thumbnail && (
                        <img
                          src={source.thumbnail}
                          alt=""
                          className="w-8 h-8 rounded object-cover flex-shrink-0"
                          onError={(e) => {
                            e.currentTarget.style.display = 'none'
                          }}
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-1">
                          <span className="text-xs font-medium truncate">
                            {source.title}
                          </span>
                          <ExternalLink className="w-3 h-3 flex-shrink-0" />
                        </div>
                        <div className="text-xs opacity-75 truncate">
                          {extractDomain(source.url)}
                        </div>
                        {source.excerpt && (
                          <div className="text-xs opacity-75 mt-1 line-clamp-2">
                            {source.excerpt}
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.a>
                ))}
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div className={`flex items-center space-x-1 mt-2 text-xs opacity-50 ${
            isUser ? 'justify-end' : 'justify-start'
          }`}>
            <Clock className="w-3 h-3" />
            <span>{formatTimestamp(message.timestamp)}</span>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
