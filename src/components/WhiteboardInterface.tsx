import React, { useC<PERSON>back, useEffect, useState } from 'react'
import <PERSON>act<PERSON><PERSON>, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
} from 'reactflow'
import 'reactflow/dist/style.css'
import { motion } from 'framer-motion'
import { useAgent } from '@/contexts/AgentContext'
import { generateId } from '@/utils'
import SourceNode from './nodes/SourceNode'
import NoteNode from './nodes/NoteNode'
import QueryNode from './nodes/QueryNode'

// Custom node types
const nodeTypes = {
  source: SourceNode,
  note: NoteNode,
  query: QueryNode,
}

export default function WhiteboardInterface() {
  const { sources, messages } = useAgent()
  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)

  // Convert sources to nodes
  useEffect(() => {
    const sourceNodes: Node[] = sources.map((source, index) => ({
      id: source.id,
      type: 'source',
      position: { 
        x: 100 + (index % 3) * 300, 
        y: 100 + Math.floor(index / 3) * 200 
      },
      data: {
        title: source.title,
        content: source.excerpt,
        url: source.url,
        thumbnail: source.thumbnail,
        source: source,
      },
    }))

    // Add query nodes for user messages
    const queryNodes: Node[] = messages
      .filter(msg => msg.role === 'user')
      .map((message, index) => ({
        id: `query-${message.id}`,
        type: 'query',
        position: { 
          x: 50, 
          y: 50 + index * 150 
        },
        data: {
          title: `Query ${index + 1}`,
          content: message.content,
        },
      }))

    setNodes([...queryNodes, ...sourceNodes])
  }, [sources, messages, setNodes])

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  )

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
  }, [])

  const addNoteNode = useCallback(() => {
    const newNode: Node = {
      id: generateId(),
      type: 'note',
      position: { x: 400, y: 300 },
      data: {
        title: 'New Note',
        content: 'Click to edit this note...',
      },
    }
    setNodes((nds) => [...nds, newNode])
  }, [setNodes])

  return (
    <div className="h-full relative bg-gray-50">
      {/* Header */}
      <div className="absolute top-0 left-0 right-0 z-10 bg-white/90 backdrop-blur-sm border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Research Whiteboard</h2>
          <div className="flex items-center space-x-2">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={addNoteNode}
              className="btn-primary text-sm"
            >
              Add Note
            </motion.button>
          </div>
        </div>
        
        {sources.length > 0 && (
          <div className="mt-2 text-sm text-gray-600">
            {sources.length} source{sources.length !== 1 ? 's' : ''} found
          </div>
        )}
      </div>

      {/* ReactFlow */}
      <div className="h-full pt-20">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
        >
          <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
          <Controls />
          <MiniMap
            nodeColor={(node) => {
              switch (node.type) {
                case 'source':
                  return '#3b82f6'
                case 'note':
                  return '#10b981'
                case 'query':
                  return '#f59e0b'
                default:
                  return '#6b7280'
              }
            }}
            className="!bg-white !border-gray-300"
          />
        </ReactFlow>
      </div>

      {/* Empty State */}
      {sources.length === 0 && messages.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 flex items-center justify-center pointer-events-none"
        >
          <div className="text-center">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              Start Your Research
            </h3>
            <p className="text-gray-600 max-w-md">
              Ask the AI assistant a question to begin. Sources and references will appear here as interactive nodes you can explore and connect.
            </p>
          </div>
        </motion.div>
      )}

      {/* Node Details Panel */}
      {selectedNode && (
        <motion.div
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          className="absolute top-20 right-4 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-20"
        >
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Node Details</h3>
            <button
              onClick={() => setSelectedNode(null)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-3">
            <div>
              <label className="text-sm font-medium text-gray-700">Title</label>
              <div className="mt-1 text-sm text-gray-900">{selectedNode.data.title}</div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700">Content</label>
              <div className="mt-1 text-sm text-gray-600 max-h-32 overflow-y-auto">
                {selectedNode.data.content}
              </div>
            </div>
            
            {selectedNode.data.url && (
              <div>
                <label className="text-sm font-medium text-gray-700">URL</label>
                <a
                  href={selectedNode.data.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-1 text-sm text-primary-600 hover:text-primary-700 block truncate"
                >
                  {selectedNode.data.url}
                </a>
              </div>
            )}
            
            <div>
              <label className="text-sm font-medium text-gray-700">Type</label>
              <div className="mt-1">
                <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                  selectedNode.type === 'source' 
                    ? 'bg-blue-100 text-blue-800'
                    : selectedNode.type === 'note'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedNode.type}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
