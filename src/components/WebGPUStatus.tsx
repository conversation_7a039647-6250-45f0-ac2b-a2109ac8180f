import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Cpu, Zap, AlertCircle } from 'lucide-react'
import { useLLM } from '@/contexts/LLMContext'
import { getWebGPUInfo } from '@/utils/webgpu'
import type { WebGPUInfo } from '@/types'

export default function WebGPUStatus() {
  const { isReady, isInitializing, error, config } = useLLM()
  const [webGPUInfo, setWebGPUInfo] = useState<WebGPUInfo | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    const loadWebGPUInfo = async () => {
      const info = await getWebGPUInfo()
      setWebGPUInfo(info)
    }
    loadWebGPUInfo()
  }, [])

  const getStatusColor = () => {
    if (error) return 'text-red-500'
    if (isInitializing) return 'text-yellow-500'
    if (isReady) return 'text-green-500'
    return 'text-gray-500'
  }

  const getStatusIcon = () => {
    if (error) return <AlertCircle className="w-4 h-4" />
    if (config.device === 'webgpu') return <Zap className="w-4 h-4" />
    return <Cpu className="w-4 h-4" />
  }

  const getStatusText = () => {
    if (error) return 'Error'
    if (isInitializing) return 'Initializing...'
    if (isReady) return config.device === 'webgpu' ? 'WebGPU Ready' : 'WASM Ready'
    return 'Not Ready'
  }

  return (
    <div className="relative">
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => setShowDetails(!showDetails)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${getStatusColor()} border-current bg-white/50 hover:bg-white/80`}
      >
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusText()}</span>
      </motion.button>

      {showDetails && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute right-0 top-full mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50"
        >
          <h3 className="font-semibold text-gray-900 mb-3">LLM Status</h3>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Model:</span>
              <span className="font-mono text-xs">{config.modelId.split('/').pop()}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Device:</span>
              <span className={`font-medium ${config.device === 'webgpu' ? 'text-green-600' : 'text-blue-600'}`}>
                {config.device.toUpperCase()}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Max Tokens:</span>
              <span>{config.maxTokens}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Temperature:</span>
              <span>{config.temperature}</span>
            </div>
          </div>

          {webGPUInfo && (
            <>
              <hr className="my-3" />
              <h4 className="font-medium text-gray-900 mb-2">WebGPU Info</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Supported:</span>
                  <span className={webGPUInfo.supported ? 'text-green-600' : 'text-red-600'}>
                    {webGPUInfo.supported ? 'Yes' : 'No'}
                  </span>
                </div>
                
                {webGPUInfo.features && (
                  <div>
                    <span className="text-gray-600">Features:</span>
                    <div className="mt-1 max-h-20 overflow-y-auto">
                      {webGPUInfo.features.map((feature, index) => (
                        <div key={index} className="text-xs font-mono text-gray-500">
                          {feature}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </>
          )}

          {error && (
            <>
              <hr className="my-3" />
              <div className="text-red-600 text-sm">
                <strong>Error:</strong> {error}
              </div>
            </>
          )}
        </motion.div>
      )}
    </div>
  )
}
