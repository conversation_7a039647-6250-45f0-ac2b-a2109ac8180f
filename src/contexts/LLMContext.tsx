import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { LLMService } from '@/services/llm'
import type { LLMConfig, LLMResponse, Message } from '@/types'
import { initializeWebGPUDevice } from '@/utils/webgpu'

interface LLMContextType {
  llmService: LLMService | null
  isInitializing: boolean
  isReady: boolean
  error: string | null
  config: LLMConfig
  updateConfig: (newConfig: Partial<LLMConfig>) => void
  generateResponse: (messages: Message[], tools?: any[]) => Promise<LLMResponse>
}

const LLMContext = createContext<LLMContextType | undefined>(undefined)

const defaultConfig: LLMConfig = {
  modelId: 'microsoft/Phi-3-mini-4k-instruct-onnx-web',
  maxTokens: 512,
  temperature: 0.7,
  topP: 0.9,
  device: 'webgpu'
}

interface LLMProviderProps {
  children: ReactNode
}

export function LLMProvider({ children }: LLMProviderProps) {
  const [llmService, setLLMService] = useState<LLMService | null>(null)
  const [isInitializing, setIsInitializing] = useState(false)
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [config, setConfig] = useState<LLMConfig>(defaultConfig)

  useEffect(() => {
    initializeLLM()
  }, [])

  const initializeLLM = async () => {
    setIsInitializing(true)
    setError(null)

    try {
      // Check WebGPU support and initialize device
      const device = await initializeWebGPUDevice()
      
      let finalConfig = { ...config }
      if (!device) {
        console.warn('WebGPU not available, falling back to WASM')
        finalConfig.device = 'wasm'
      }

      const service = new LLMService(finalConfig)
      await service.initialize()
      
      setLLMService(service)
      setConfig(finalConfig)
      setIsReady(true)
      
      console.log('LLM service initialized successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Failed to initialize LLM service:', err)
    } finally {
      setIsInitializing(false)
    }
  }

  const updateConfig = (newConfig: Partial<LLMConfig>) => {
    setConfig(prev => ({ ...prev, ...newConfig }))
    
    // Reinitialize if service exists
    if (llmService) {
      initializeLLM()
    }
  }

  const generateResponse = async (
    messages: Message[],
    tools?: any[]
  ): Promise<LLMResponse> => {
    if (!llmService || !isReady) {
      throw new Error('LLM service not ready')
    }

    // Convert messages to the format expected by the LLM service
    const formattedMessages = messages.map(msg => ({
      role: msg.role,
      content: msg.content
    }))

    return await llmService.generateResponse(formattedMessages, tools)
  }

  const value: LLMContextType = {
    llmService,
    isInitializing,
    isReady,
    error,
    config,
    updateConfig,
    generateResponse
  }

  return (
    <LLMContext.Provider value={value}>
      {children}
    </LLMContext.Provider>
  )
}

export function useLLM() {
  const context = useContext(LLMContext)
  if (context === undefined) {
    throw new Error('useLLM must be used within a LLMProvider')
  }
  return context
}
