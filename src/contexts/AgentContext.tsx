import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react'
import { AgentService } from '@/services/agent'
import { useLLM } from './LLMContext'
import type { Message, Source, AgentState } from '@/types'
import { generateId } from '@/utils'

interface AgentContextType {
  messages: Message[]
  sources: Source[]
  agentState: AgentState
  isProcessing: boolean
  sendMessage: (content: string) => Promise<void>
  clearConversation: () => void
  addSource: (source: Source) => void
  removeSource: (sourceId: string) => void
}

const AgentContext = createContext<AgentContextType | undefined>(undefined)

interface AgentProviderProps {
  children: ReactNode
}

export function AgentProvider({ children }: AgentProviderProps) {
  const { llmService, isReady } = useLLM()
  const [messages, setMessages] = useState<Message[]>([])
  const [sources, setSources] = useState<Source[]>([])
  const [agentState, setAgentState] = useState<AgentState>({
    isThinking: false,
    currentTool: undefined,
    toolProgress: undefined,
    lastError: undefined
  })
  const [isProcessing, setIsProcessing] = useState(false)

  const sendMessage = useCallback(async (content: string) => {
    if (!llmService || !isReady || isProcessing) {
      return
    }

    setIsProcessing(true)

    // Add user message
    const userMessage: Message = {
      id: generateId(),
      role: 'user',
      content,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])

    try {
      // Create agent service instance
      const agentService = new AgentService(llmService)

      // Process the query
      const response = await agentService.processQuery(
        content,
        messages,
        (state) => setAgentState(state),
        (source) => addSource(source)
      )

      // Add assistant response
      setMessages(prev => [...prev, response])

    } catch (error) {
      console.error('Failed to process message:', error)
      
      // Add error message
      const errorMessage: Message = {
        id: generateId(),
        role: 'assistant',
        content: 'I apologize, but I encountered an error while processing your request. Please try again.',
        timestamp: new Date()
      }
      
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsProcessing(false)
      setAgentState(prev => ({ ...prev, isThinking: false }))
    }
  }, [llmService, isReady, isProcessing, messages])

  const clearConversation = useCallback(() => {
    setMessages([])
    setSources([])
    setAgentState({
      isThinking: false,
      currentTool: undefined,
      toolProgress: undefined,
      lastError: undefined
    })
  }, [])

  const addSource = useCallback((source: Source) => {
    setSources(prev => {
      // Check if source already exists
      const exists = prev.some(s => s.url === source.url)
      if (exists) return prev
      
      return [...prev, source]
    })
  }, [])

  const removeSource = useCallback((sourceId: string) => {
    setSources(prev => prev.filter(s => s.id !== sourceId))
  }, [])

  const value: AgentContextType = {
    messages,
    sources,
    agentState,
    isProcessing,
    sendMessage,
    clearConversation,
    addSource,
    removeSource
  }

  return (
    <AgentContext.Provider value={value}>
      {children}
    </AgentContext.Provider>
  )
}

export function useAgent() {
  const context = useContext(AgentContext)
  if (context === undefined) {
    throw new Error('useAgent must be used within an AgentProvider')
  }
  return context
}
