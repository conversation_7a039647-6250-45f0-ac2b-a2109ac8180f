import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import ChatInterface from './components/ChatInterface'
import WhiteboardInterface from './components/WhiteboardInterface'
import WebGPUStatus from './components/WebGPUStatus'
import { LLMProvider } from './contexts/LLMContext'
import { AgentProvider } from './contexts/AgentContext'
import { checkWebGPUSupport } from './utils/webgpu'

function App() {
  const [webGPUSupported, setWebGPUSupported] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkSupport = async () => {
      try {
        const supported = await checkWebGPUSupport()
        setWebGPUSupported(supported)
      } catch (error) {
        console.error('Error checking WebGPU support:', error)
        setWebGPUSupported(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkSupport()
  }, [])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center"
        >
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-secondary-600">Initializing WebGPU LLM Agent...</p>
        </motion.div>
      </div>
    )
  }

  if (webGPUSupported === false) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="card p-8 max-w-md text-center"
        >
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-secondary-900 mb-4">
            WebGPU Not Supported
          </h1>
          <p className="text-secondary-600 mb-6">
            Your browser doesn't support WebGPU, which is required for local LLM inference.
            Please use a modern browser like Chrome 113+ or Edge 113+.
          </p>
          <a
            href="https://caniuse.com/webgpu"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-primary inline-block"
          >
            Check Browser Compatibility
          </a>
        </motion.div>
      </div>
    )
  }

  return (
    <LLMProvider>
      <AgentProvider>
        <div className="min-h-screen bg-secondary-50">
          <header className="bg-white border-b border-secondary-200 px-4 py-3">
            <div className="flex items-center justify-between max-w-7xl mx-auto">
              <motion.h1
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="text-2xl font-bold text-secondary-900"
              >
                🤖 Local LLM Web Agent
              </motion.h1>
              <WebGPUStatus />
            </div>
          </header>

          <main className="flex h-[calc(100vh-73px)]">
            {/* Chat Interface - Left Side */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="w-1/3 border-r border-secondary-200 bg-white"
            >
              <ChatInterface />
            </motion.div>

            {/* Whiteboard Interface - Right Side */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="flex-1"
            >
              <WhiteboardInterface />
            </motion.div>
          </main>
        </div>
      </AgentProvider>
    </LLMProvider>
  )
}

export default App
