/**
 * Debug utilities for troubleshooting WebGPU and Transformers.js issues
 */

export function logBrowserInfo() {
  console.group('🔍 Browser Environment Debug Info')
  
  // Basic browser info
  console.log('User Agent:', navigator.userAgent)
  console.log('Platform:', navigator.platform)
  console.log('Language:', navigator.language)
  
  // WebGPU availability
  console.log('WebGPU Available:', 'gpu' in navigator)
  
  // SharedArrayBuffer support (required for some models)
  console.log('SharedArrayBuffer Available:', typeof SharedArrayBuffer !== 'undefined')
  
  // Cross-origin isolation status
  console.log('Cross-Origin Isolated:', crossOriginIsolated)
  
  // Memory info (if available)
  if ('memory' in performance) {
    const memory = (performance as any).memory
    console.log('Memory Info:', {
      usedJSHeapSize: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
      totalJSHeapSize: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
      jsHeapSizeLimit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`
    })
  }
  
  console.groupEnd()
}

export async function logWebGPUInfo() {
  console.group('🎮 WebGPU Debug Info')
  
  if (!('gpu' in navigator)) {
    console.warn('WebGPU not available in this browser')
    console.groupEnd()
    return
  }

  try {
    const adapter = await navigator.gpu.requestAdapter()
    if (!adapter) {
      console.warn('No WebGPU adapter available')
      console.groupEnd()
      return
    }

    console.log('Adapter Info:', {
      vendor: adapter.info?.vendor || 'Unknown',
      architecture: adapter.info?.architecture || 'Unknown',
      device: adapter.info?.device || 'Unknown',
      description: adapter.info?.description || 'Unknown'
    })

    console.log('Adapter Features:', Array.from(adapter.features))
    
    console.log('Adapter Limits:', {
      maxTextureDimension1D: adapter.limits.maxTextureDimension1D,
      maxTextureDimension2D: adapter.limits.maxTextureDimension2D,
      maxBufferSize: adapter.limits.maxBufferSize,
      maxComputeWorkgroupStorageSize: adapter.limits.maxComputeWorkgroupStorageSize
    })

    // Try to create a device
    const device = await adapter.requestDevice()
    console.log('Device created successfully')
    device.destroy()

  } catch (error) {
    console.error('WebGPU initialization error:', error)
  }
  
  console.groupEnd()
}

export function logTransformersInfo() {
  console.group('🤖 Transformers.js Debug Info')
  
  try {
    // Try to import and log transformers info
    import('@huggingface/transformers').then(({ env }) => {
      console.log('Transformers.js Environment:', {
        allowRemoteModels: env.allowRemoteModels,
        allowLocalModels: env.allowLocalModels,
        remoteURL: env.remoteURL,
        localURL: env.localURL
      })
      
      if (env.backends) {
        console.log('Available Backends:', Object.keys(env.backends))
      }
    }).catch(error => {
      console.error('Failed to load Transformers.js:', error)
    })
  } catch (error) {
    console.error('Transformers.js import error:', error)
  }
  
  console.groupEnd()
}

export async function runFullDiagnostics() {
  console.log('🚀 Running Full Diagnostics...')
  
  logBrowserInfo()
  await logWebGPUInfo()
  logTransformersInfo()
  
  console.log('✅ Diagnostics Complete')
}

// Auto-run diagnostics in development
if (import.meta.env.DEV) {
  runFullDiagnostics()
}
