import type { WebGPUInfo } from '@/types'

/**
 * Check if WebGPU is supported in the current browser
 */
export async function checkWebGPUSupport(): Promise<boolean> {
  // Check if we're in a browser environment
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    console.warn('WebGPU not supported: Not in browser environment')
    return false
  }

  if (!navigator.gpu) {
    console.warn('WebGPU not supported: navigator.gpu is undefined')
    return false
  }

  try {
    const adapter = await navigator.gpu.requestAdapter({
      powerPreference: 'high-performance'
    })

    if (!adapter) {
      console.warn('WebGPU not supported: No adapter available')
      return false
    }

    // Check for required features
    const requiredFeatures = []
    const availableFeatures = Array.from(adapter.features)

    for (const feature of requiredFeatures) {
      if (!availableFeatures.includes(feature)) {
        console.warn(`WebGPU not supported: Missing feature ${feature}`)
        return false
      }
    }

    const device = await adapter.requestDevice({
      requiredFeatures: requiredFeatures as GPUFeatureName[]
    })

    if (!device) {
      console.warn('WebGPU not supported: No device available')
      return false
    }

    // Clean up
    device.destroy()
    console.log('WebGPU is supported and available')
    return true
  } catch (error) {
    console.error('WebGPU support check failed:', error)
    return false
  }
}

/**
 * Get detailed WebGPU information
 */
export async function getWebGPUInfo(): Promise<WebGPUInfo> {
  const info: WebGPUInfo = {
    supported: false
  }

  if (!navigator.gpu) {
    return info
  }

  try {
    const adapter = await navigator.gpu.requestAdapter()
    if (!adapter) {
      return info
    }

    const device = await adapter.requestDevice()
    if (!device) {
      return info
    }

    info.supported = true
    info.adapter = adapter
    info.device = device
    info.features = Array.from(adapter.features)
    info.limits = {
      maxTextureDimension1D: adapter.limits.maxTextureDimension1D,
      maxTextureDimension2D: adapter.limits.maxTextureDimension2D,
      maxTextureDimension3D: adapter.limits.maxTextureDimension3D,
      maxTextureArrayLayers: adapter.limits.maxTextureArrayLayers,
      maxBindGroups: adapter.limits.maxBindGroups,
      maxDynamicUniformBuffersPerPipelineLayout: adapter.limits.maxDynamicUniformBuffersPerPipelineLayout,
      maxDynamicStorageBuffersPerPipelineLayout: adapter.limits.maxDynamicStorageBuffersPerPipelineLayout,
      maxSampledTexturesPerShaderStage: adapter.limits.maxSampledTexturesPerShaderStage,
      maxSamplersPerShaderStage: adapter.limits.maxSamplersPerShaderStage,
      maxStorageBuffersPerShaderStage: adapter.limits.maxStorageBuffersPerShaderStage,
      maxStorageTexturesPerShaderStage: adapter.limits.maxStorageTexturesPerShaderStage,
      maxUniformBuffersPerShaderStage: adapter.limits.maxUniformBuffersPerShaderStage,
      maxUniformBufferBindingSize: adapter.limits.maxUniformBufferBindingSize,
      maxStorageBufferBindingSize: adapter.limits.maxStorageBufferBindingSize,
      maxBufferSize: adapter.limits.maxBufferSize,
      maxVertexBuffers: adapter.limits.maxVertexBuffers,
      maxVertexAttributes: adapter.limits.maxVertexAttributes,
      maxVertexBufferArrayStride: adapter.limits.maxVertexBufferArrayStride,
      maxComputeWorkgroupStorageSize: adapter.limits.maxComputeWorkgroupStorageSize,
      maxComputeInvocationsPerWorkgroup: adapter.limits.maxComputeInvocationsPerWorkgroup,
      maxComputeWorkgroupSizeX: adapter.limits.maxComputeWorkgroupSizeX,
      maxComputeWorkgroupSizeY: adapter.limits.maxComputeWorkgroupSizeY,
      maxComputeWorkgroupSizeZ: adapter.limits.maxComputeWorkgroupSizeZ,
      maxComputeWorkgroupsPerDimension: adapter.limits.maxComputeWorkgroupsPerDimension
    }

    return info
  } catch (error) {
    console.error('Failed to get WebGPU info:', error)
    return info
  }
}

/**
 * Initialize WebGPU device for LLM inference
 */
export async function initializeWebGPUDevice(): Promise<GPUDevice | null> {
  try {
    if (!navigator.gpu) {
      throw new Error('WebGPU not supported')
    }

    const adapter = await navigator.gpu.requestAdapter({
      powerPreference: 'high-performance'
    })

    if (!adapter) {
      throw new Error('No WebGPU adapter available')
    }

    const device = await adapter.requestDevice({
      requiredFeatures: [],
      requiredLimits: {}
    })

    // Set up error handling
    device.addEventListener('uncapturederror', (event) => {
      console.error('WebGPU uncaptured error:', event.error)
    })

    return device
  } catch (error) {
    console.error('Failed to initialize WebGPU device:', error)
    return null
  }
}
