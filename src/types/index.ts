// Core types for the LLM Web Agent application

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  sources?: Source[]
  toolCalls?: ToolCall[]
}

export interface Source {
  id: string
  title: string
  url: string
  excerpt: string
  thumbnail?: string
  timestamp: Date
  relevanceScore?: number
}

export interface ToolCall {
  id: string
  name: string
  arguments: Record<string, any>
  result?: any
  status: 'pending' | 'success' | 'error'
  timestamp: Date
}

export interface WhiteboardNode {
  id: string
  type: 'source' | 'note' | 'query'
  position: { x: number; y: number }
  data: {
    title: string
    content: string
    url?: string
    thumbnail?: string
    source?: Source
  }
  style?: {
    width?: number
    height?: number
    backgroundColor?: string
    borderColor?: string
  }
}

export interface WhiteboardEdge {
  id: string
  source: string
  target: string
  type?: string
  animated?: boolean
  style?: Record<string, any>
}

export interface LLMConfig {
  modelId: string
  maxTokens: number
  temperature: number
  topP: number
  device: 'webgpu' | 'wasm'
}

export interface AgentState {
  isThinking: boolean
  currentTool?: string
  toolProgress?: number
  lastError?: string
}

export interface SearchResult {
  title: string
  url: string
  snippet: string
  thumbnail?: string
}

export interface WebPageContent {
  url: string
  title: string
  content: string
  excerpt: string
  metadata: {
    description?: string
    keywords?: string[]
    author?: string
    publishDate?: string
  }
}

export interface Tool {
  name: string
  description: string
  parameters: Record<string, any>
  execute: (args: Record<string, any>) => Promise<any>
}

export interface LLMResponse {
  content: string
  toolCalls?: ToolCall[]
  finishReason: 'stop' | 'length' | 'tool_calls'
}

export interface WebGPUInfo {
  supported: boolean
  adapter?: GPUAdapter
  device?: GPUDevice
  features?: string[]
  limits?: Record<string, number>
}
