import type { Tool, SearchResult, WebPageContent } from '@/types'
import { extractDomain, isValidUrl, retry } from '@/utils'

// Note: You'll need to set your Google Custom Search API key and Search Engine ID
const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY || ''
const GOOGLE_SEARCH_ENGINE_ID = import.meta.env.VITE_GOOGLE_SEARCH_ENGINE_ID || ''

/**
 * Web Search Tool using Google Custom Search API
 */
export const webSearchTool: Tool = {
  name: 'web_search',
  description: 'Search the web for information using Google Custom Search API',
  parameters: {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: 'The search query to execute'
      },
      num_results: {
        type: 'number',
        description: 'Number of results to return (1-10)',
        default: 5
      }
    },
    required: ['query']
  },
  execute: async (args: { query: string; num_results?: number }): Promise<SearchResult[]> => {
    if (!GOOGLE_API_KEY || !GOOGLE_SEARCH_ENGINE_ID) {
      throw new Error('Google Custom Search API credentials not configured. Please set VITE_GOOGLE_API_KEY and VITE_GOOGLE_SEARCH_ENGINE_ID environment variables.')
    }

    const { query, num_results = 5 } = args
    const numResults = Math.min(Math.max(num_results, 1), 10)

    try {
      const response = await retry(async () => {
        const url = new URL('https://www.googleapis.com/customsearch/v1')
        url.searchParams.set('key', GOOGLE_API_KEY)
        url.searchParams.set('cx', GOOGLE_SEARCH_ENGINE_ID)
        url.searchParams.set('q', query)
        url.searchParams.set('num', numResults.toString())

        const res = await fetch(url.toString())
        if (!res.ok) {
          throw new Error(`Search API error: ${res.status} ${res.statusText}`)
        }
        return res.json()
      })

      const results: SearchResult[] = (response.items || []).map((item: any) => ({
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        thumbnail: item.pagemap?.cse_thumbnail?.[0]?.src || 
                  item.pagemap?.cse_image?.[0]?.src ||
                  `https://www.google.com/s2/favicons?domain=${extractDomain(item.link)}&sz=64`
      }))

      return results
    } catch (error) {
      console.error('Web search failed:', error)
      throw new Error(`Web search failed: ${error}`)
    }
  }
}

/**
 * Web Page Fetch Tool
 */
export const webPageFetchTool: Tool = {
  name: 'fetch_webpage',
  description: 'Fetch and extract content from a web page',
  parameters: {
    type: 'object',
    properties: {
      url: {
        type: 'string',
        description: 'The URL of the web page to fetch'
      },
      extract_length: {
        type: 'number',
        description: 'Maximum length of content to extract (default: 2000)',
        default: 2000
      }
    },
    required: ['url']
  },
  execute: async (args: { url: string; extract_length?: number }): Promise<WebPageContent> => {
    const { url, extract_length = 2000 } = args

    if (!isValidUrl(url)) {
      throw new Error('Invalid URL provided')
    }

    try {
      // Use a CORS proxy for fetching web pages
      const proxyUrl = `https://api.allorigins.win/get?url=${encodeURIComponent(url)}`
      
      const response = await retry(async () => {
        const res = await fetch(proxyUrl)
        if (!res.ok) {
          throw new Error(`Failed to fetch page: ${res.status} ${res.statusText}`)
        }
        return res.json()
      })

      const html = response.contents
      
      // Parse HTML content (basic extraction)
      const content = extractTextFromHTML(html)
      const title = extractTitleFromHTML(html) || extractDomain(url)
      const metadata = extractMetadataFromHTML(html)
      
      // Create excerpt
      const excerpt = content.length > extract_length 
        ? content.substring(0, extract_length) + '...'
        : content

      return {
        url,
        title,
        content,
        excerpt,
        metadata
      }
    } catch (error) {
      console.error('Web page fetch failed:', error)
      throw new Error(`Failed to fetch webpage: ${error}`)
    }
  }
}

/**
 * Extract text content from HTML
 */
function extractTextFromHTML(html: string): string {
  // Create a temporary DOM element to parse HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html

  // Remove script and style elements
  const scripts = tempDiv.querySelectorAll('script, style, nav, header, footer, aside')
  scripts.forEach(el => el.remove())

  // Get text content
  let text = tempDiv.textContent || tempDiv.innerText || ''
  
  // Clean up whitespace
  text = text.replace(/\s+/g, ' ').trim()
  
  return text
}

/**
 * Extract title from HTML
 */
function extractTitleFromHTML(html: string): string | null {
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i)
  return titleMatch ? titleMatch[1].trim() : null
}

/**
 * Extract metadata from HTML
 */
function extractMetadataFromHTML(html: string): {
  description?: string
  keywords?: string[]
  author?: string
  publishDate?: string
} {
  const metadata: any = {}

  // Extract description
  const descMatch = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
  if (descMatch) {
    metadata.description = descMatch[1]
  }

  // Extract keywords
  const keywordsMatch = html.match(/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
  if (keywordsMatch) {
    metadata.keywords = keywordsMatch[1].split(',').map(k => k.trim())
  }

  // Extract author
  const authorMatch = html.match(/<meta[^>]*name=["\']author["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i)
  if (authorMatch) {
    metadata.author = authorMatch[1]
  }

  // Extract publish date (various formats)
  const datePatterns = [
    /<meta[^>]*property=["\']article:published_time["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i,
    /<meta[^>]*name=["\']date["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i,
    /<meta[^>]*name=["\']publish_date["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i
  ]

  for (const pattern of datePatterns) {
    const match = html.match(pattern)
    if (match) {
      metadata.publishDate = match[1]
      break
    }
  }

  return metadata
}

/**
 * Get all available tools
 */
export function getAllTools(): Tool[] {
  return [webSearchTool, webPageFetchTool]
}

/**
 * Get tool by name
 */
export function getToolByName(name: string): Tool | undefined {
  return getAllTools().find(tool => tool.name === name)
}
