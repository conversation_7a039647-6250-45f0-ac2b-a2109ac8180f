import type { Message, ToolCall, Source, AgentState, LLMResponse } from '@/types'
import { LLMService } from './llm'
import { getAllTools, getToolByName } from './tools'
import { generateId } from '@/utils'

export class AgentService {
  private llmService: LLMService
  private tools = getAllTools()
  private state: AgentState = {
    isThinking: false,
    currentTool: undefined,
    toolProgress: undefined,
    lastError: undefined
  }

  constructor(llmService: LLMService) {
    this.llmService = llmService
  }

  /**
   * Process a user query and generate a response with tool usage
   */
  async processQuery(
    query: string,
    conversationHistory: Message[] = [],
    onStateChange?: (state: AgentState) => void,
    onSourceFound?: (source: Source) => void
  ): Promise<Message> {
    this.updateState({ isThinking: true }, onStateChange)

    try {
      // Prepare messages for LLM
      const messages: Message[] = [
        ...conversationHistory,
        {
          id: generateId(),
          role: 'user',
          content: query,
          timestamp: new Date()
        }
      ]

      // Generate initial response
      let response = await this.generateLLMResponse(messages)
      let finalContent = response.content
      const sources: Source[] = []
      const allToolCalls: ToolCall[] = []

      // Process tool calls if any
      if (response.toolCalls && response.toolCalls.length > 0) {
        const toolResults = await this.executeToolCalls(
          response.toolCalls,
          onStateChange,
          onSourceFound
        )

        // Add tool results to sources
        sources.push(...toolResults.sources)
        allToolCalls.push(...toolResults.toolCalls)

        // Generate follow-up response with tool results
        const toolResultsMessage: Message = {
          id: generateId(),
          role: 'system',
          content: this.formatToolResults(toolResults.toolCalls),
          timestamp: new Date()
        }

        const followUpResponse = await this.generateLLMResponse([
          ...messages,
          {
            id: generateId(),
            role: 'assistant',
            content: response.content,
            timestamp: new Date(),
            toolCalls: response.toolCalls
          },
          toolResultsMessage
        ])

        finalContent = followUpResponse.content
      }

      this.updateState({ isThinking: false }, onStateChange)

      return {
        id: generateId(),
        role: 'assistant',
        content: finalContent,
        timestamp: new Date(),
        sources: sources.length > 0 ? sources : undefined,
        toolCalls: allToolCalls.length > 0 ? allToolCalls : undefined
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      this.updateState({ 
        isThinking: false, 
        lastError: errorMessage 
      }, onStateChange)

      return {
        id: generateId(),
        role: 'assistant',
        content: `I apologize, but I encountered an error while processing your request: ${errorMessage}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * Generate LLM response
   */
  private async generateLLMResponse(messages: Message[]): Promise<LLMResponse> {
    return await this.llmService.generateResponse(messages, this.tools)
  }

  /**
   * Execute tool calls
   */
  private async executeToolCalls(
    toolCalls: ToolCall[],
    onStateChange?: (state: AgentState) => void,
    onSourceFound?: (source: Source) => void
  ): Promise<{ toolCalls: ToolCall[]; sources: Source[] }> {
    const executedToolCalls: ToolCall[] = []
    const sources: Source[] = []

    for (let i = 0; i < toolCalls.length; i++) {
      const toolCall = toolCalls[i]
      const progress = ((i + 1) / toolCalls.length) * 100

      this.updateState({
        currentTool: toolCall.name,
        toolProgress: progress
      }, onStateChange)

      try {
        const tool = getToolByName(toolCall.name)
        if (!tool) {
          throw new Error(`Tool '${toolCall.name}' not found`)
        }

        const result = await tool.execute(toolCall.arguments)
        
        // Update tool call with result
        const executedToolCall: ToolCall = {
          ...toolCall,
          result,
          status: 'success'
        }
        executedToolCalls.push(executedToolCall)

        // Convert results to sources if applicable
        if (toolCall.name === 'web_search' && Array.isArray(result)) {
          const searchSources = result.map((item: any) => ({
            id: generateId(),
            title: item.title,
            url: item.url,
            excerpt: item.snippet,
            thumbnail: item.thumbnail,
            timestamp: new Date(),
            relevanceScore: 0.8 // Default relevance score
          }))
          
          sources.push(...searchSources)
          searchSources.forEach(source => onSourceFound?.(source))
        }

        if (toolCall.name === 'fetch_webpage' && result) {
          const webSource: Source = {
            id: generateId(),
            title: result.title,
            url: result.url,
            excerpt: result.excerpt,
            timestamp: new Date(),
            relevanceScore: 0.9
          }
          
          sources.push(webSource)
          onSourceFound?.(webSource)
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        
        const failedToolCall: ToolCall = {
          ...toolCall,
          result: { error: errorMessage },
          status: 'error'
        }
        executedToolCalls.push(failedToolCall)
        
        console.error(`Tool execution failed for ${toolCall.name}:`, error)
      }
    }

    this.updateState({
      currentTool: undefined,
      toolProgress: undefined
    }, onStateChange)

    return { toolCalls: executedToolCalls, sources }
  }

  /**
   * Format tool results for LLM context
   */
  private formatToolResults(toolCalls: ToolCall[]): string {
    let content = 'Tool execution results:\n\n'
    
    toolCalls.forEach(toolCall => {
      content += `${toolCall.name}:\n`
      if (toolCall.status === 'success') {
        if (toolCall.name === 'web_search' && Array.isArray(toolCall.result)) {
          content += `Found ${toolCall.result.length} search results:\n`
          toolCall.result.forEach((result: any, index: number) => {
            content += `${index + 1}. ${result.title}\n   URL: ${result.url}\n   Snippet: ${result.snippet}\n\n`
          })
        } else if (toolCall.name === 'fetch_webpage' && toolCall.result) {
          content += `Page Title: ${toolCall.result.title}\n`
          content += `URL: ${toolCall.result.url}\n`
          content += `Content: ${toolCall.result.excerpt}\n\n`
        } else {
          content += `Result: ${JSON.stringify(toolCall.result, null, 2)}\n\n`
        }
      } else {
        content += `Error: ${toolCall.result?.error || 'Unknown error'}\n\n`
      }
    })

    return content
  }

  /**
   * Update agent state
   */
  private updateState(
    newState: Partial<AgentState>,
    onStateChange?: (state: AgentState) => void
  ) {
    this.state = { ...this.state, ...newState }
    onStateChange?.(this.state)
  }

  /**
   * Get current agent state
   */
  getState(): AgentState {
    return { ...this.state }
  }

  /**
   * Get available tools
   */
  getAvailableTools() {
    return this.tools.map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }))
  }
}
