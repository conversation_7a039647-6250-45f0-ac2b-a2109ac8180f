import { pipeline, env } from '@huggingface/transformers'
import type { LLMConfig, LLMResponse, ToolCall } from '@/types'
import { generateId } from '@/utils'

// Configure transformers environment
env.allowRemoteModels = true
env.allowLocalModels = false

// Configure ONNX runtime paths - safer approach
try {
  if (env.backends?.onnx?.wasm) {
    env.backends.onnx.wasm.wasmPaths = 'https://cdn.jsdelivr.net/npm/onnxruntime-web@1.17.1/dist/'
  }
} catch (error) {
  console.warn('Could not configure WASM paths:', error)
}

export class LLMService {
  private pipeline: any = null
  private isInitialized = false
  private config: LLMConfig

  constructor(config: LLMConfig) {
    this.config = config
  }

  /**
   * Initialize the LLM pipeline
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    let lastError: Error | null = null

    // Try WebGPU first, then fall back to WASM
    const attempts = [
      { device: 'webgpu' as const, dtype: 'q4f16' as const },
      { device: 'wasm' as const, dtype: 'q4f16' as const }
    ]

    for (const attempt of attempts) {
      try {
        console.log(`Attempting to initialize LLM pipeline with device: ${attempt.device}`)

        this.pipeline = await pipeline(
          'text-generation',
          this.config.modelId,
          {
            device: attempt.device,
            dtype: attempt.dtype,
            progress_callback: (progress: any) => {
              if (progress.status === 'downloading') {
                console.log(`Downloading ${progress.name}: ${Math.round(progress.progress)}%`)
              }
            }
          }
        )

        // Update config with successful device
        this.config.device = attempt.device as 'webgpu' | 'wasm'
        this.isInitialized = true

        console.log(`LLM pipeline initialized successfully with device: ${attempt.device}`)
        return

      } catch (error) {
        console.warn(`Failed to initialize with ${attempt.device}:`, error)
        lastError = error as Error

        // Clean up failed pipeline
        this.pipeline = null

        // If this was WebGPU, try WASM next
        if (attempt.device === 'webgpu') {
          console.log('WebGPU failed, trying WASM fallback...')
          continue
        }
      }
    }

    // If we get here, all attempts failed
    throw new Error(`LLM initialization failed: ${lastError?.message || 'Unknown error'}`)
  }

  /**
   * Generate response from the LLM
   */
  async generateResponse(
    messages: Array<{ role: string; content: string }>,
    tools?: Array<any>
  ): Promise<LLMResponse> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      // Format messages for the current model
      const prompt = this.formatMessagesForModel(messages, tools)
      
      console.log('Generating response for prompt:', prompt.substring(0, 200) + '...')

      const result = await this.pipeline(prompt, {
        max_new_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        top_p: this.config.topP,
        do_sample: true,
        return_full_text: false,
      })

      const generatedText = result[0].generated_text.trim()
      
      // Parse tool calls if present
      const { content, toolCalls } = this.parseResponse(generatedText)

      return {
        content,
        toolCalls,
        finishReason: 'stop'
      }
    } catch (error) {
      console.error('LLM generation failed:', error)
      throw new Error(`Response generation failed: ${error}`)
    }
  }

  /**
   * Format messages for the current model
   */
  private formatMessagesForModel(
    messages: Array<{ role: string; content: string }>,
    tools?: Array<any>
  ): string {
    let prompt = ''

    // Add system message with tool definitions
    if (tools && tools.length > 0) {
      prompt += 'You are a helpful AI assistant with access to the following tools:\n\n'
      tools.forEach(tool => {
        prompt += `${tool.name}: ${tool.description}\n`
        prompt += `Parameters: ${JSON.stringify(tool.parameters)}\n\n`
      })
      prompt += 'To use a tool, respond with: <tool_call>{"name": "tool_name", "arguments": {...}}</tool_call>\n'
      prompt += 'You can make multiple tool calls in a single response.\n\n'
    }

    prompt += 'You are an intelligent web research assistant. Help users find information by searching the web and analyzing content.\n\n'

    // Add conversation messages
    messages.forEach(message => {
      if (message.role === 'user') {
        prompt += `Human: ${message.content}\n\n`
      } else if (message.role === 'assistant') {
        prompt += `Assistant: ${message.content}\n\n`
      }
    })

    prompt += 'Assistant: '
    return prompt
  }

  /**
   * Parse response to extract content and tool calls
   */
  private parseResponse(response: string): { content: string; toolCalls?: ToolCall[] } {
    const toolCallRegex = /<tool_call>(.*?)<\/tool_call>/gs
    const toolCalls: ToolCall[] = []
    let content = response

    let match
    while ((match = toolCallRegex.exec(response)) !== null) {
      try {
        const toolCallData = JSON.parse(match[1])
        toolCalls.push({
          id: generateId(),
          name: toolCallData.name,
          arguments: toolCallData.arguments,
          status: 'pending',
          timestamp: new Date()
        })
        
        // Remove tool call from content
        content = content.replace(match[0], '').trim()
      } catch (error) {
        console.warn('Failed to parse tool call:', match[1])
      }
    }

    return {
      content: content.trim(),
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined
    }
  }

  /**
   * Check if the LLM is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.pipeline !== null
  }

  /**
   * Get model information
   */
  getModelInfo(): { modelId: string; config: LLMConfig } {
    return {
      modelId: this.config.modelId,
      config: this.config
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.pipeline) {
      // Note: Transformers.js doesn't have explicit cleanup methods
      // Memory will be garbage collected
      this.pipeline = null
      this.isInitialized = false
    }
  }
}
