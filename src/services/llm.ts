import { pipeline, env } from '@huggingface/transformers'
import type { LLMConfig, LLMResponse, ToolCall } from '@/types'
import { generateId } from '@/utils'

// Configure transformers to use WebGPU
env.backends.onnx.wasm.wasmPaths = 'https://cdn.jsdelivr.net/npm/onnxruntime-web@1.16.3/dist/'

export class LLMService {
  private pipeline: any = null
  private tokenizer: any = null
  private isInitialized = false
  private config: LLMConfig

  constructor(config: LLMConfig) {
    this.config = config
  }

  /**
   * Initialize the LLM pipeline
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      console.log('Initializing LLM pipeline...')
      
      // Use Phi-3 Mini for WebGPU inference
      this.pipeline = await pipeline(
        'text-generation',
        'microsoft/Phi-3-mini-4k-instruct-onnx-web',
        {
          device: this.config.device,
          dtype: 'fp16',
        }
      )

      this.tokenizer = this.pipeline.tokenizer
      this.isInitialized = true
      
      console.log('LLM pipeline initialized successfully')
    } catch (error) {
      console.error('Failed to initialize LLM pipeline:', error)
      throw new Error(`LLM initialization failed: ${error}`)
    }
  }

  /**
   * Generate response from the LLM
   */
  async generateResponse(
    messages: Array<{ role: string; content: string }>,
    tools?: Array<any>
  ): Promise<LLMResponse> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    try {
      // Format messages for Phi-3
      const prompt = this.formatMessagesForPhi3(messages, tools)
      
      console.log('Generating response for prompt:', prompt.substring(0, 200) + '...')

      const result = await this.pipeline(prompt, {
        max_new_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        top_p: this.config.topP,
        do_sample: true,
        return_full_text: false,
      })

      const generatedText = result[0].generated_text.trim()
      
      // Parse tool calls if present
      const { content, toolCalls } = this.parseResponse(generatedText)

      return {
        content,
        toolCalls,
        finishReason: 'stop'
      }
    } catch (error) {
      console.error('LLM generation failed:', error)
      throw new Error(`Response generation failed: ${error}`)
    }
  }

  /**
   * Format messages for Phi-3 chat template
   */
  private formatMessagesForPhi3(
    messages: Array<{ role: string; content: string }>,
    tools?: Array<any>
  ): string {
    let prompt = '<|system|>\n'
    
    // Add system message with tool definitions
    if (tools && tools.length > 0) {
      prompt += 'You are a helpful AI assistant with access to the following tools:\n\n'
      tools.forEach(tool => {
        prompt += `${tool.name}: ${tool.description}\n`
        prompt += `Parameters: ${JSON.stringify(tool.parameters)}\n\n`
      })
      prompt += 'To use a tool, respond with: <tool_call>{"name": "tool_name", "arguments": {...}}</tool_call>\n'
      prompt += 'You can make multiple tool calls in a single response.\n\n'
    }
    
    prompt += 'You are an intelligent web research assistant. Help users find information by searching the web and analyzing content.\n<|end|>\n'

    // Add conversation messages
    messages.forEach(message => {
      if (message.role === 'user') {
        prompt += `<|user|>\n${message.content}\n<|end|>\n`
      } else if (message.role === 'assistant') {
        prompt += `<|assistant|>\n${message.content}\n<|end|>\n`
      }
    })

    prompt += '<|assistant|>\n'
    return prompt
  }

  /**
   * Parse response to extract content and tool calls
   */
  private parseResponse(response: string): { content: string; toolCalls?: ToolCall[] } {
    const toolCallRegex = /<tool_call>(.*?)<\/tool_call>/gs
    const toolCalls: ToolCall[] = []
    let content = response

    let match
    while ((match = toolCallRegex.exec(response)) !== null) {
      try {
        const toolCallData = JSON.parse(match[1])
        toolCalls.push({
          id: generateId(),
          name: toolCallData.name,
          arguments: toolCallData.arguments,
          status: 'pending',
          timestamp: new Date()
        })
        
        // Remove tool call from content
        content = content.replace(match[0], '').trim()
      } catch (error) {
        console.warn('Failed to parse tool call:', match[1])
      }
    }

    return {
      content: content.trim(),
      toolCalls: toolCalls.length > 0 ? toolCalls : undefined
    }
  }

  /**
   * Check if the LLM is ready
   */
  isReady(): boolean {
    return this.isInitialized && this.pipeline !== null
  }

  /**
   * Get model information
   */
  getModelInfo(): { modelId: string; config: LLMConfig } {
    return {
      modelId: this.config.modelId,
      config: this.config
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.pipeline) {
      // Note: Transformers.js doesn't have explicit cleanup methods
      // Memory will be garbage collected
      this.pipeline = null
      this.tokenizer = null
      this.isInitialized = false
    }
  }
}
