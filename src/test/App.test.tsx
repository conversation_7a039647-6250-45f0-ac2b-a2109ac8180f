import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import App from '../App'

// Mock the WebGPU check
vi.mock('../utils/webgpu', () => ({
  checkWebGPUSupport: vi.fn(() => Promise.resolve(true)),
  getWebGPUInfo: vi.fn(() => Promise.resolve({ supported: true })),
  initializeWebGPUDevice: vi.fn(() => Promise.resolve({})),
}))

// Mock the LLM service
vi.mock('../services/llm', () => ({
  LLMService: vi.fn().mockImplementation(() => ({
    initialize: vi.fn(() => Promise.resolve()),
    isReady: vi.fn(() => true),
    generateResponse: vi.fn(() => Promise.resolve({ content: 'Test response', finishReason: 'stop' })),
  })),
}))

describe('App', () => {
  it('renders the main application', async () => {
    render(<App />)
    
    // Should show loading initially
    expect(screen.getByText('Initializing WebGPU LLM Agent...')).toBeInTheDocument()
  })

  it('shows WebGPU not supported message when WebGPU is unavailable', async () => {
    // Mock WebGPU as not supported
    const { checkWebGPUSupport } = await import('../utils/webgpu')
    vi.mocked(checkWebGPUSupport).mockResolvedValueOnce(false)

    render(<App />)
    
    // Wait for the check to complete and error message to show
    await screen.findByText('WebGPU Not Supported')
    expect(screen.getByText(/Your browser doesn't support WebGPU/)).toBeInTheDocument()
  })
})
