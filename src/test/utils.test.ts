import { describe, it, expect } from 'vitest'
import { 
  generateId, 
  formatTimestamp, 
  truncateText, 
  extractDomain, 
  isValidUrl,
  calculateSimilarity 
} from '../utils'

describe('Utility Functions', () => {
  describe('generateId', () => {
    it('generates unique IDs', () => {
      const id1 = generateId()
      const id2 = generateId()
      expect(id1).not.toBe(id2)
      expect(typeof id1).toBe('string')
      expect(id1.length).toBeGreaterThan(0)
    })
  })

  describe('formatTimestamp', () => {
    it('formats recent timestamps correctly', () => {
      const now = new Date()
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000)
      
      expect(formatTimestamp(now)).toBe('just now')
      expect(formatTimestamp(fiveMinutesAgo)).toBe('5m ago')
    })
  })

  describe('truncateText', () => {
    it('truncates long text', () => {
      const longText = 'This is a very long text that should be truncated'
      const result = truncateText(longText, 20)
      expect(result).toBe('This is a very lo...')
      expect(result.length).toBe(20)
    })

    it('returns original text if shorter than limit', () => {
      const shortText = 'Short text'
      const result = truncateText(shortText, 20)
      expect(result).toBe(shortText)
    })
  })

  describe('extractDomain', () => {
    it('extracts domain from URL', () => {
      expect(extractDomain('https://www.example.com/path')).toBe('example.com')
      expect(extractDomain('https://subdomain.example.com')).toBe('subdomain.example.com')
    })

    it('handles invalid URLs', () => {
      expect(extractDomain('not-a-url')).toBe('not-a-url')
    })
  })

  describe('isValidUrl', () => {
    it('validates URLs correctly', () => {
      expect(isValidUrl('https://example.com')).toBe(true)
      expect(isValidUrl('http://example.com')).toBe(true)
      expect(isValidUrl('not-a-url')).toBe(false)
      expect(isValidUrl('')).toBe(false)
    })
  })

  describe('calculateSimilarity', () => {
    it('calculates text similarity', () => {
      const text1 = 'hello world'
      const text2 = 'hello universe'
      const text3 = 'completely different'
      
      const similarity1 = calculateSimilarity(text1, text2)
      const similarity2 = calculateSimilarity(text1, text3)
      
      expect(similarity1).toBeGreaterThan(similarity2)
      expect(similarity1).toBeGreaterThan(0)
      expect(similarity1).toBeLessThanOrEqual(1)
    })

    it('returns 1 for identical texts', () => {
      const text = 'identical text'
      expect(calculateSimilarity(text, text)).toBe(1)
    })
  })
})
