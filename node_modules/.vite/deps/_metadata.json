{"hash": "e7b791ef", "configHash": "536d9cf3", "lockfileHash": "634baf91", "browserHash": "daff8da3", "optimized": {"onnxruntime-web": {"src": "../../onnxruntime-web/dist/ort.bundle.min.mjs", "file": "onnxruntime-web.js", "fileHash": "3e8754f2", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "865544f0", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "efa8e36a", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a9abf89c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "28fc02ec", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "02313e2d", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "00df0bcd", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "88fdc363", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c4c21eba", "needsInterop": true}, "reactflow": {"src": "../../reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "c18fdcb3", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "2d795a23", "needsInterop": false}}, "chunks": {"chunk-67DEV5DB": {"file": "chunk-67DEV5DB.js"}, "chunk-I773Y2XN": {"file": "chunk-I773Y2XN.js"}, "chunk-LK32TJAX": {"file": "chunk-LK32TJAX.js"}}}