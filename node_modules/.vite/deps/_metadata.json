{"hash": "2c0473e0", "configHash": "cfe01e82", "lockfileHash": "634baf91", "browserHash": "16c54592", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "f6e64310", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "f68a184b", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "1aacc0e4", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7ae5689a", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "862b2a88", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "f807b331", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "b89fef78", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "ac38cd0c", "needsInterop": true}, "reactflow": {"src": "../../reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "81046981", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "4fdd63f2", "needsInterop": false}}, "chunks": {"chunk-GKJBSOWT": {"file": "chunk-GKJBSOWT.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}