{"hash": "c31c508a", "configHash": "8980a1e2", "lockfileHash": "b74f5ba0", "browserHash": "1f234b44", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "71cba85c", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "0d916f39", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "64e59475", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4719b85e", "needsInterop": true}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "feb2551d", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "d3be0153", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "4761e411", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "0d77f70b", "needsInterop": true}, "reactflow": {"src": "../../reactflow/dist/esm/index.mjs", "file": "reactflow.js", "fileHash": "4c78f05c", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "538a4e57", "needsInterop": false}}, "chunks": {"chunk-YQ5BCTVV": {"file": "chunk-YQ5BCTVV.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}