{"version": 3, "file": "program-manager.js", "sourceRoot": "", "sources": ["program-manager.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,2DAAyC;AAEzC,iDAAoD;AAEpD,2DAAuD;AACvD,+CAAsD;AAKtD;;;;;;;;GAQG;AACH,MAAa,cAAc;IAKzB,YACS,QAA4B,EAC5B,SAAuB,EACvB,qBAA4C;QAF5C,aAAQ,GAAR,QAAQ,CAAoB;QAC5B,cAAS,GAAT,SAAS,CAAc;QACvB,0BAAqB,GAArB,qBAAqB,CAAuB;QAEnD,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IAC/B,CAAC;IACD,WAAW,CAAC,GAAY;QACtB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC;IACD,WAAW,CAAC,GAAY,EAAE,QAAkB;QAC1C,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC/B,CAAC;IACD,GAAG,CAAC,aAAuB,EAAE,MAAqB,EAAE,MAAmB;QACrE,IAAI,CAAC,QAAQ,CAAC,KAAK,CACjB,IAAI,EACJ,sBAAsB,aAAa,CAAC,WAAW,CAAC,IAAI,IAAI,gBAAgB,EAAE,EAC1E,GAAG,EAAE;YACH,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAC;YACtC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACvB,IAAI;gBACF,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBACxB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACzB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;iBACpD;gBACD,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,gBAAgB,EAAE,aAAa,CAAC,WAAW,CAAC,SAAS,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;aACtG;YAAC,OAAO,GAAG,EAAE;gBACZ,mBAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACvE,MAAM,GAAG,CAAC;aACX;YACD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,kBAAkB,EAAE,GAAG,EAAE;gBACtD,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC;QACL,CAAC,EACD,IAAI,CAAC,SAAS,CACf,CAAC;IACJ,CAAC;IACD,OAAO;QACL,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IACpE,CAAC;IACD,KAAK,CAAC,WAAwB,EAAE,mBAAoC,EAAE,mBAAkC;QACtG,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,sBAAsB,EAAE,GAAG,EAAE;YACjE,MAAM,YAAY,GAAG,IAAI,oCAAgB,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;YACjH,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,EAAE,CAAC;YAC7C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACzC,MAAM,QAAQ,GAAG;gBACf,WAAW;gBACX,OAAO;gBACP,gBAAgB,EAAE,IAAI,CAAC,mBAAmB,CACxC,OAAO,EACP,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,EAC3C,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAC3C;gBACD,eAAe,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;aAClD,CAAC;YACF,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IACS,OAAO,CAAC,gBAAwB;QACxC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,mBAAM,CAAC,OAAO,CAAC,iBAAiB,EAAE,wDAAwD,CAAC,CAAC;YAC5F,MAAM,kBAAkB,GAAG,IAAA,mCAAqB,EAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACzE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;SACvG;QACD,IAAI,wBAAG,CAAC,KAAK,EAAE;YACb,mBAAM,CAAC,OAAO,CACZ,iBAAiB,EACjB;EACN,gBAAgB;CACjB,CACM,CAAC;SACH;QACD,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,gBAAgB,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;QACrG,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;QAC5E,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QACxC,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,UAAU,CAAC,EAAe;QACxB,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;QACvB,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;QACzB,mBAAM,CAAC,OAAO,CACZ,iBAAiB,EACjB,8CAA8C,KAAK,IAAI,MAAM,WAAW,EAAE,CAAC,KAAK,UAAU,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,CAC3G,CAAC;QACF,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAC9D,CAAC;IACD,cAAc,CAAC,eAAyC;QACtD,MAAM,cAAc,GAAG,eAAe,CAAC,QAAQ,CAAC;QAChD,MAAM,kBAAkB,GAAG,eAAe,CAAC,YAAY,CAAC;QACxD,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;QACvE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;IAC9B,CAAC;IACD,YAAY,CACV,gBAA2C,EAC3C,SAA4B,EAC5B,QAAuB;QAEvB,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,KAAK,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,gBAAgB,EAAE;YACpE,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC;YAC3D,IAAI,IAAI,KAAK,WAAW,IAAI,CAAC,KAAK,EAAE;gBAClC,MAAM,IAAI,KAAK,CAAC,aAAa,IAAI,8CAA8C,CAAC,CAAC;aAClF;YACD,QAAQ,IAAI,EAAE;gBACZ,KAAK,WAAW;oBACd,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,QAAQ,EAAE,eAAe,CAAC,CAAC;oBACvE,eAAe,EAAE,CAAC;oBAClB,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,WAAW,EAAE;wBACf,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAiB,CAAC,CAAC;qBAC5C;yBAAM;wBACL,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAe,CAAC,CAAC;qBACzC;oBACD,MAAM;gBACR,KAAK,KAAK;oBACR,IAAI,WAAW,EAAE;wBACf,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAiB,CAAC,CAAC;qBAC5C;yBAAM;wBACL,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAe,CAAC,CAAC;qBACzC;oBACD,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAC;aACvD;SACF;IACH,CAAC;IACD,WAAW,CAAC,EAAe,EAAE,aAAmC,EAAE,QAAgB;QAChF,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;IAC3E,CAAC;IACD,kBAAkB,CAAC,OAAqB;QACtC,OAAO;YACL,QAAQ,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,UAAU,CAAC;YACrD,YAAY,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,CAAC;SAC9D,CAAC;IACJ,CAAC;IACD,mBAAmB,CACjB,OAAqB,EACrB,QAAmB,EACnB,SAA0B;QAE1B,MAAM,gBAAgB,GAA8B,EAAE,CAAC;QACvD,IAAI,QAAQ,EAAE;YACZ,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;gBAC9B,gBAAgB,CAAC,IAAI,CAAC;oBACpB,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,WAAW;oBACjB,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC;iBACpD,CAAC,CAAC;aACJ;SACF;QACD,IAAI,SAAS,EAAE;YACb,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,gBAAgB,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACnG;SACF;QACD,OAAO,gBAAgB,CAAC;IAC1B,CAAC;IACD,kBAAkB,CAAC,OAAqB,EAAE,IAAY;QACpD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,MAAM,SAAS,GAAG,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvD,IAAI,SAAS,KAAK,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,aAAa,CAAC,CAAC;SAC/C;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,iBAAiB,CAAC,OAAqB,EAAE,IAAY;QACnD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7B,MAAM,iBAAiB,GAAW,EAAE,CAAC,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACtE,OAAO,iBAAiB,CAAC;IAC3B,CAAC;CACF;AAtLD,wCAsLC"}