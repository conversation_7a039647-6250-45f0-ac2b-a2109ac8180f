{"version": 3, "file": "webgl-context-factory.js", "sourceRoot": "", "sources": ["webgl-context-factory.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,iDAA0C;AAE1C,mDAA+C;AAE/C,MAAM,KAAK,GAA0C,EAAE,CAAC;AAExD;;;;GAIG;AACH,SAAgB,kBAAkB,CAAC,SAA8B;IAC/D,IAAI,OAAiC,CAAC;IACtC,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,QAAQ,CAAC,IAAI,QAAQ,IAAI,KAAK,EAAE;QAC/D,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;KACxB;SAAM,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,KAAK,OAAO,CAAC,IAAI,OAAO,IAAI,KAAK,EAAE;QACpE,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC;KACvB;IAED,IAAI,CAAC,OAAO,EAAE;QACZ,IAAI;YACF,uDAAuD;YACvD,MAAM,eAAe,GAAG,qBAAqB,EAAE,CAAC;YAChD,OAAO,GAAG,qBAAqB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;SAC7D;QAAC,OAAO,CAAC,EAAE;YACV,4DAA4D;YAC5D,MAAM,MAAM,GAAG,YAAY,EAAE,CAAC;YAC9B,OAAO,GAAG,qBAAqB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SACpD;KACF;IAED,SAAS,GAAG,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC;IACpE,MAAM,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;IAEtB,KAAK,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;IAE3B,IAAI,EAAE,CAAC,aAAa,EAAE,EAAE;QACtB,OAAO,KAAK,CAAC,SAAS,CAAC,CAAC;QACxB,OAAO,kBAAkB,CAAC,SAAS,CAAC,CAAC;KACtC;IAED,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;IAC1B,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;IAC5B,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;IACrB,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;IACtB,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,CAAC,CAAC;IACnC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;IAC/B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;IAC3B,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;IACxB,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAErB,OAAO,OAAO,CAAC;AACjB,CAAC;AAzCD,gDAyCC;AAED,SAAgB,qBAAqB,CAAC,MAAyB,EAAE,SAA8B;IAC7F,MAAM,iBAAiB,GAA2B;QAChD,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,KAAK;QACZ,SAAS,EAAE,KAAK;QAChB,OAAO,EAAE,KAAK;QACd,qBAAqB,EAAE,KAAK;QAC5B,kBAAkB,EAAE,KAAK;QACzB,4BAA4B,EAAE,KAAK;KACpC,CAAC;IACF,IAAI,EAAgC,CAAC;IACrC,MAAM,EAAE,GAAG,iBAAiB,CAAC;IAC7B,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,QAAQ,EAAE;QACxC,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACrC,IAAI,EAAE,EAAE;YACN,IAAI;gBACF,OAAO,IAAI,4BAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aAChC;YAAC,OAAO,GAAG,EAAE;gBACZ,mBAAM,CAAC,OAAO,CAAC,kBAAkB,EAAE,kEAAkE,GAAG,EAAE,CAAC,CAAC;aAC7G;SACF;KACF;IACD,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,OAAO,EAAE;QACvC,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,IAAK,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,EAAE,CAA2B,CAAC;QAC9G,IAAI,EAAE,EAAE;YACN,IAAI;gBACF,OAAO,IAAI,4BAAY,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;aAChC;YAAC,OAAO,GAAG,EAAE;gBACZ,mBAAM,CAAC,OAAO,CACZ,kBAAkB,EAClB,yFAAyF,GAAG,EAAE,CAC/F,CAAC;aACH;SACF;KACF;IAED,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC5C,CAAC;AArCD,sDAqCC;AAKD,SAAS,YAAY;IACnB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;QACnC,MAAM,IAAI,SAAS,CAAC,oDAAoD,CAAC,CAAC;KAC3E;IACD,MAAM,MAAM,GAAsB,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACnE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;IACjB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,qBAAqB;IAC5B,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;QAC1C,MAAM,IAAI,SAAS,CAAC,qEAAqE,CAAC,CAAC;KAC5F;IACD,OAAO,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACnC,CAAC"}