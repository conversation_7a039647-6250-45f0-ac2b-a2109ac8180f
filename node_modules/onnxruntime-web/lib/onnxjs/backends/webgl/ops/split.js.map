{"version": 3, "file": "split.js", "sourceRoot": "", "sources": ["split.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,gFAAuG;AAIvG,wCAAqD;AAErD,oCAAoD;AAQpD,MAAM,oBAAoB,GAAG;IAC3B,IAAI,EAAE,OAAO;IACb,UAAU,EAAE,CAAC,GAAG,CAAC;IACjB,UAAU,EAAE,CAAC,mBAAW,CAAC,QAAQ,CAAC;CACnC,CAAC;AAEK,MAAM,KAAK,GAA4C,CAC5D,gBAAuC,EACvC,MAAgB,EAChB,UAA2B,EACjB,EAAE;IACZ,cAAc,CAAC,MAAM,CAAC,CAAC;IAEvB,MAAM,IAAI,GAAG,gBAAS,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC7E,MAAM,KAAK,GAAG,eAAe,CAAC,gBAAgB,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IAC1E,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;QAC9B,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,GAAG,CAClB;YACE,GAAG,oBAAoB;YACvB,SAAS,EAAE,GAAG,UAAU,CAAC,QAAQ,IAAI,CAAC,EAAE;YACxC,GAAG,EAAE,GAAG,EAAE,CAAC,sBAAsB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;SACpF,EACD,MAAM,CACP,CACF,CAAC;KACH;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAxBW,QAAA,KAAK,SAwBhB;AAEK,MAAM,oBAAoB,GAA4C,CAAC,IAAgB,EAAmB,EAAE;IACjH,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;IACnD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACvC,OAAO,IAAA,sDAA2B,EAAC,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AAClE,CAAC,CAAC;AALW,QAAA,oBAAoB,wBAK/B;AAEF,MAAM,eAAe,GAAG,CACtB,iBAAwC,EACxC,MAAgB,EAChB,IAAY,EACZ,UAA2B,EACnB,EAAE;IACV,MAAM,CAAC,EAAE,OAAO,CAAC,GAAG,gBAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IACxG,OAAO,OAAO,CAAC,MAAM,CAAC;AACxB,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG,CAC7B,iBAAwC,EACxC,KAAa,EACb,UAA2B,EAC3B,IAAY,EACZ,KAAa,EACA,EAAE;IACf,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,gBAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;IAC1G,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,WAAW,CAAC,MAAM,CAAC;IAChC,MAAM,YAAY,GAAG;kCACW,IAAI;kBACpB,IAAI,QAAQ,MAAM;;;KAG/B,CAAC;IACJ,OAAO;QACL,GAAG,oBAAoB;QACvB,SAAS,EAAE,GAAG,UAAU,CAAC,QAAQ,IAAI,KAAK,EAAE;QAC5C,MAAM,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,WAAW,EAAE,mBAAW,CAAC,QAAQ,EAAE;QAClF,YAAY;KACb,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,MAAgB,EAAQ,EAAE;IAChD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;KAC9C;IAED,IACE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM;QACzB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;QAC1B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;QAC1B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ;QAC3B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO;QAC1B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ;QAC3B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,EACzB;QACA,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;KACxC;AACH,CAAC,CAAC"}