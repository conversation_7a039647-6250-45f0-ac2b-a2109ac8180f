{"version": 3, "file": "texture-data-encoder.js", "sourceRoot": "", "sources": ["texture-data-encoder.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;AAElC,iDAA0C;AAkC1C;;;GAGG;AACH,MAAa,qBAAqB;IAKhC,YAAY,EAA0B,EAAE,QAAQ,GAAG,CAAC;QAClD,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;YACrB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC7B;aAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,OAAO,CAAC;YACjC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK,CAAC;YAC5B,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC7B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IACD,MAAM,CAAC,GAA0B,EAAE,WAAmB;QACpD,IAAI,MAAoB,CAAC;QACzB,IAAI,MAAoB,CAAC;QACzB,IAAI,GAAG,CAAC,WAAW,KAAK,YAAY,EAAE;YACpC,mBAAM,CAAC,OAAO,CAAC,SAAS,EAAE,yDAAyD,CAAC,CAAC;YACrF,MAAM,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;SAChC;QACD,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,EAAE;YAC/C,mBAAM,CAAC,OAAO,CAAC,SAAS,EAAE,gDAAgD,CAAC,CAAC;YAC5E,MAAM,GAAG,GAAmB,CAAC;YAC7B,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAiB,CAAC;YACvE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAC3C;aAAM;YACL,MAAM,GAAG,GAAmB,CAAC;YAC7B,MAAM,GAAG,MAAM,CAAC;SACjB;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IACD,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,MAAM,CAAC,MAA6B,EAAE,QAAgB;QACpD,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;YAC1B,MAAM,YAAY,GAAI,MAAuB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC/G,OAAO,YAAY,CAAC;SACrB;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAiB,CAAC;IACtD,CAAC;CACF;AAhDD,sDAgDC;AACD;;GAEG;AACH,MAAa,oBAAoB;IAK/B,YAAY,EAAyB,EAAE,QAAQ,GAAG,CAAC,EAAE,WAAoB;QACvE,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE;YACpC,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,EAAE,CAAC,KAAK,CAAC;IAC7C,CAAC;IACD,MAAM,CAAC,GAAiB,EAAE,WAAmB;QAC3C,IAAI,IAAI,GAAG,GAAG,CAAC;QACf,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;YAC1B,mBAAM,CAAC,OAAO,CAAC,SAAS,EAAE,+BAA+B,CAAC,CAAC;YAC3D,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAiB,CAAC;YAClD,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SAC1C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,MAAM,CAAC,MAA6B,EAAE,QAAgB;QACpD,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;YAC1B,MAAM,YAAY,GAAI,MAAuB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YAC/G,OAAO,YAAY,CAAC;SACrB;QACD,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAiB,CAAC;IACtD,CAAC;CACF;AAjCD,oDAiCC;AAED,MAAa,gBAAgB;IAK3B,YAAY,EAAyB,EAAE,QAAQ,GAAG,CAAC;QADnD,gBAAW,GAAG,CAAC,CAAC;QAEd,IAAI,QAAQ,KAAK,CAAC,EAAE;YAClB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,KAAK,CAAC;YAC/B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,aAAa;YACrC,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC7B;aAAM,IAAI,QAAQ,KAAK,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,IAAI,CAAC;YAC9B,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC;YACtB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC,aAAa,CAAC;YACpC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;SAC7B;aAAM;YACL,MAAM,IAAI,KAAK,CAAC,+BAA+B,QAAQ,EAAE,CAAC,CAAC;SAC5D;IACH,CAAC;IACD,MAAM,CAAC,GAAe,EAAE,YAAoB;QAC1C,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;IACpE,CAAC;IACD,QAAQ,CAAC,IAAY;QACnB,OAAO,IAAI,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IACjD,CAAC;IACD,MAAM,CAAC,MAA6B,EAAE,QAAgB;QACpD,IAAI,MAAM,YAAY,UAAU,EAAE;YAChC,OAAO,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;SACrC;QACD,MAAM,IAAI,KAAK,CAAC,uBAAuB,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC;IAC/D,CAAC;CACF;AAhCD,4CAgCC"}