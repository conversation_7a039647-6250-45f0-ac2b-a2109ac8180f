{"version": 3, "file": "tensor.js", "sourceRoot": "", "sources": ["tensor.ts"], "names": [], "mappings": ";AAAA,4DAA4D;AAC5D,kCAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAElC,qDAAuC;AACvC,gDAAwB;AAExB,+EAAiE;AACjE,qDAAkD;AAClD,iCAAgE;AAuChE,MAAa,MAAM;IACjB;;OAEG;IACH,IAAI,IAAI;QACN,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,YAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;gBAC7B,MAAM,IAAI,KAAK,CAAC,4FAA4F,CAAC,CAAC;aAC/G;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;SACnB;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC1B,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;SAChD;QAED,OAAO,IAAI,CAAC,IAAyB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,IAAI,WAAW;QACb,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,OAAO,CAAC;YACb,KAAK,MAAM,CAAC;YACZ,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO,CAAC;YACb,KAAK,OAAO,CAAC;YACb,KAAK,QAAQ,CAAC;YACd,KAAK,MAAM;gBACT,OAAO,IAAI,CAAC,IAA0B,CAAC;YAEzC;gBACE,MAAM,IAAI,SAAS,CAAC,4EAA4E,CAAC,CAAC;SACrG;IACH,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,QAAQ,IAAI,CAAC,IAAI,EAAE;YACjB,KAAK,SAAS,CAAC;YACf,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,IAAwB,CAAC;YAEvC;gBACE,MAAM,IAAI,SAAS,CAAC,2CAA2C,CAAC,CAAC;SACpE;IACH,CAAC;IAED;;;OAGG;IACH,IAAI,UAAU;QACZ,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;YAC1B,OAAO,IAAI,CAAC,IAAyB,CAAC;SACvC;QACD,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;IAC5D,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,OAA0B;QAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAS,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,GAAG,CAAC,OAA0B,EAAE,KAAkD;QAChF,IAAI,CAAC,IAAI,CAAC,gBAAS,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,CAAC;IACtE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,iBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACzD;QACD,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAQD;;OAEG;IACH,IAAI,OAAO;QACT,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,gBAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACrD;QACD,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;IACE;;OAEG;IACa,IAAuB;IACvC;;OAEG;IACa,IAAqB,EAC7B,YAA2B,EAC3B,iBAAqC,EACrC,KAAkB;IAC1B;;OAEG;IACa,SAAe,sBAAI,CAAC,MAAM,EAAE;QAX5B,SAAI,GAAJ,IAAI,CAAmB;QAIvB,SAAI,GAAJ,IAAI,CAAiB;QAC7B,iBAAY,GAAZ,YAAY,CAAe;QAC3B,sBAAiB,GAAjB,iBAAiB,CAAoB;QACrC,UAAK,GAAL,KAAK,CAAa;QAIV,WAAM,GAAN,MAAM,CAAsB;QAE5C,IAAI,CAAC,IAAI,GAAG,gBAAS,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,MAAM,KAAK,GAAG,YAAY,KAAK,SAAS,IAAI,iBAAiB,KAAK,SAAS,IAAI,KAAK,KAAK,SAAS,CAAC;QAEnG,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;gBACzB,MAAM,IAAI,UAAU,CAAC,uCAAuC,CAAC,CAAC;aAC/D;SACF;QAED,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,IAAI,KAAK,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,QAAQ,CAAC,CAAC,EAAE;gBAChG,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAC;aACvD;YAED,IAAI,KAAK,EAAE;gBACT,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAS,IAAI,CAAC,CAAC;aACtC;SACF;aAAM;YACL,IAAI,KAAK,KAAK,SAAS,EAAE;gBACvB,MAAM,WAAW,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;gBAC9C,IAAI,CAAC,CAAC,KAAK,YAAY,WAAW,CAAC,EAAE;oBACnC,MAAM,IAAI,SAAS,CAAC,wBAAwB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC;iBACjE;aACF;YAED,IAAI,KAAK,EAAE;gBACT,MAAM,GAAG,GAAG,IAAI,WAAW,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;gBACjD,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;aACpC;SACF;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,SAAS,CAAC,WAA8B;QAC7C,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;QACD,MAAM,IAAI,GAAG,gBAAS,CAAC,uBAAuB,CAAC,WAAW,CAAC,QAAS,CAAC,CAAC;QACtE,MAAM,IAAI,GAAG,gBAAS,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAK,CAAC,CAAC;QAE9D,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,oEAAoE;YACpE,eAAe;YACf,WAAW,CAAC,UAAW,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;gBACzC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAA,uBAAgB,EAAC,GAAG,CAAC,CAAC;YACxC,CAAC,CAAC,CAAC;SACJ;aAAM,IACL,WAAW,CAAC,OAAO;YACnB,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ;YAClD,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,CAAC,EAClC;YACA,8CAA8C;YAE9C,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAC7B,WAAW,CAAC,OAAO,CAAC,MAAM,EAC1B,WAAW,CAAC,OAAO,CAAC,UAAU,EAC9B,WAAW,CAAC,OAAO,CAAC,UAAU,CAC/B,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,QAAS,CAAC,CAAC;YACvD,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,WAAW,CAAC;YAE5D,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,WAAW,KAAK,CAAC,EAAE;gBACtD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC3C;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,MAAM,CAAC,GAAG,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,QAAS,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;gBACxE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACjB;SACF;aAAM;YACL,4BAA4B;YAC5B,IAAI,KAA2B,CAAC;YAChC,QAAQ,WAAW,CAAC,QAAQ,EAAE;gBAC5B,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;oBAClC,KAAK,GAAG,WAAW,CAAC,SAAU,CAAC;oBAC/B,MAAM;gBACR,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACtC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACpC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI;oBACjC,KAAK,GAAG,WAAW,CAAC,SAAU,CAAC;oBAC/B,MAAM;gBACR,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;oBAClC,KAAK,GAAG,WAAW,CAAC,SAAU,CAAC;oBAC/B,MAAM;gBACR,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;oBACnC,KAAK,GAAG,WAAW,CAAC,UAAW,CAAC;oBAChC,MAAM;gBACR,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACtC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;oBACnC,KAAK,GAAG,WAAW,CAAC,UAAW,CAAC;oBAChC,MAAM;gBACR;oBACE,wBAAwB;oBACxB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;aACvC;YAED,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,EAAE;gBACzC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;aACrE;YAED,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;gBAChC,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,IAAI,cAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBACxB,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;iBACvD;qBAAM;oBACL,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;iBACnB;aACF;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAyC,EAAE,IAAuB,EAAE,IAAqB;QACvG,OAAO,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,SAAwB;QAC3C,IAAI,CAAC,SAAS,EAAE;YACd,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;SAChE;QACD,MAAM,IAAI,GAAG,gBAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,CAAC;QAC1D,MAAM,IAAI,GAAG,gBAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;QAErE,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAErC,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,oEAAoE;YACpE,eAAe;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,EAAE,EAAE;gBACrD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;aACzC;SACF;aAAM,IACL,SAAS,CAAC,YAAY,EAAE;YACxB,OAAO,SAAS,CAAC,aAAa,EAAE,KAAK,QAAQ;YAC7C,SAAS,CAAC,aAAa,EAAE,GAAG,CAAC,EAC7B;YACA,8CAA8C;YAE9C,8BAA8B;YAC9B,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAC5B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAC7B,SAAS,CAAC,YAAY,EAAG,CAAC,MAAM,EAChC,SAAS,CAAC,YAAY,EAAG,CAAC,UAAU,EACpC,SAAS,CAAC,aAAa,EAAE,CAC1B,CAAC;YACF,MAAM,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,SAAS,CAAC,aAAa,EAAE,GAAG,WAAW,CAAC;YAEvD,IAAI,SAAS,CAAC,aAAa,EAAE,GAAG,WAAW,KAAK,CAAC,EAAE;gBACjD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;aAC1C;YACD,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC3C;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/B,MAAM,CAAC,GAAG,SAAS,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;gBACvE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;aACjB;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AA7TD,wBA6TC;AAED,SAAS,MAAM,CAAC,IAAqB;IACnC,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,CAAC,CAAC;QACX,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,CAAC,CAAC;QACX,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ,CAAC;QACd,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;QACX,KAAK,SAAS;YACZ,OAAO,CAAC,CAAC;QACX;YACE,MAAM,IAAI,KAAK,CAAC,qCAAqC,IAAI,EAAE,CAAC,CAAC;KAChE;AACH,CAAC;AAED,SAAS,WAAW,CAAC,IAAuD;IAC1E,QAAQ,IAAI,EAAE;QACZ,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;QACpC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI;YACjC,OAAO,CAAC,CAAC;QACX,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;QACtC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,CAAC,CAAC;QACX,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,CAAC,CAAC;QACX,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;QACrC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;QACtC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,CAAC,CAAC;QACX;YACE,MAAM,IAAI,KAAK,CAAC,qCAAqC,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC3F;AACH,CAAC;AAED,SAAS,UAAU,CAAC,UAAuB,EAAE,IAAqB;IAChE,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACrD,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAqB;IAChD,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO;YACV,OAAO,UAAU,CAAC;QACpB,KAAK,MAAM;YACT,OAAO,SAAS,CAAC;QACnB,KAAK,OAAO;YACV,OAAO,UAAU,CAAC;QACpB,KAAK,QAAQ;YACX,OAAO,WAAW,CAAC;QACrB,KAAK,OAAO;YACV,OAAO,UAAU,CAAC;QACpB,KAAK,QAAQ;YACX,OAAO,WAAW,CAAC;QACrB,KAAK,OAAO;YACV,OAAO,aAAa,CAAC;QACvB,KAAK,SAAS;YACZ,OAAO,YAAY,CAAC;QACtB,KAAK,SAAS;YACZ,OAAO,YAAY,CAAC;QACtB;YACE,2BAA2B;YAC3B,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;KACxC;AACH,CAAC;AAED,wDAAwD;AACxD,SAAS,YAAY,CAAC,CAAO,EAAE,IAAuD;IACpF,wBAAwB;IACxB,IAAI,IAAI,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE;QACpF,IAAI,CAAC,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;YAC/D,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;SAC/C;KACF;SAAM,IACL,IAAI,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;QACzC,IAAI,KAAK,MAAM,CAAC,cAAc,CAAC,MAAM;QACrC,IAAI,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;QACzC,IAAI,KAAK,MAAM,CAAC,cAAc,CAAC,MAAM,EACrC;QACA,IAAI,CAAC,CAAC,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACrD,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;SAChD;KACF;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,oBAAoB,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC5E;IAED,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;AACtB,CAAC;AAED,kCAAkC;AAClC,SAAS,SAAS,CAChB,IAAc,EACd,IAAuD,EACvD,UAAkB;IAElB,QAAQ,IAAI,EAAE;QACZ,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;QACpC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACnC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI;YACjC,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1C,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3C,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QACzC,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC1C,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK;YAClC,OAAO,YAAY,CACjB,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,EAC5F,IAAI,CACL,CAAC;QACJ,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC3C,KAAK,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM;YACnC,OAAO,YAAY,CACjB,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAC3F,IAAI,CACL,CAAC;QACJ;YACE,MAAM,IAAI,KAAK,CAAC,sCAAsC,WAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC5F;AACH,CAAC"}