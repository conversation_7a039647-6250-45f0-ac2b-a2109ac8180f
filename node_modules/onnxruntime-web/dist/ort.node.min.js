/*!
 * ONNX Runtime Web v1.22.0-dev.20250409-89f8206ba4
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
"use strict";var gt=Object.create;var ie=Object.defineProperty;var wt=Object.getOwnPropertyDescriptor;var yt=Object.getOwnPropertyNames;var St=Object.getPrototypeOf,ht=Object.prototype.hasOwnProperty;var C=(e,t)=>()=>(e&&(t=e(e=0)),t);var xe=(e,t)=>{for(var n in t)ie(e,n,{get:t[n],enumerable:!0})},ae=(e,t,n,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of yt(t))!ht.call(e,r)&&r!==n&&ie(e,r,{get:()=>t[r],enumerable:!(o=wt(t,r))||o.enumerable});return e},R=(e,t,n)=>(ae(e,t,"default"),n&&ae(n,t,"default")),Et=(e,t,n)=>(n=e!=null?gt(St(e)):{},ae(t||!e||!e.__esModule?ie(n,"default",{value:e,enumerable:!0}):n,e)),Ae=e=>ae(ie({},"__esModule",{value:!0}),e);var N,ue=C(()=>{"use strict";N=!!(typeof process<"u"&&process.versions&&process.versions.node)});var Ot,Tt,$,De,Me,vt,Pt,It,Lt,Ce,ke,Se=C(()=>{"use strict";ue();Ot=N||typeof location>"u"?void 0:location.origin,Tt=()=>{if(!N)return typeof document<"u"?document.currentScript?.src:typeof self<"u"?self.location?.href:void 0},$=Tt(),De=()=>{if($&&!$.startsWith("blob:"))return $.substring(0,$.lastIndexOf("/")+1)},Me=(e,t)=>{try{let n=t??$;return(n?new URL(e,n):new URL(e)).origin===Ot}catch{return!1}},vt=(e,t)=>{let n=t??$;try{return(n?new URL(e,n):new URL(e)).href}catch{return}},Pt=(e,t)=>`${t??"./"}${e}`,It=async e=>{let n=await(await fetch(e,{credentials:"same-origin"})).blob();return URL.createObjectURL(n)},Lt=async e=>(await import(/*webpackIgnore:true*/e)).default,Ce=void 0,ke=async(e,t,n)=>{if(!e&&!t&&Ce&&$&&Me($))return[void 0,Ce];{let o="ort-wasm-simd-threaded.mjs",r=e??vt(o,t),a=!N&&n&&r&&!Me(r,t),s=a?await It(r):r??Pt(o,t);return[a?s:void 0,await Lt(s)]}}});var he,Ee,ce,We,_t,Ut,xt,Fe,y,J=C(()=>{"use strict";Se();Ee=!1,ce=!1,We=!1,_t=()=>{if(typeof SharedArrayBuffer>"u")return!1;try{return typeof MessageChannel<"u"&&new MessageChannel().port1.postMessage(new SharedArrayBuffer(1)),WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,5,4,1,3,1,1,10,11,1,9,0,65,0,254,16,2,0,26,11]))}catch{return!1}},Ut=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,4,1,96,0,0,3,2,1,0,10,30,1,28,0,65,0,253,15,253,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,253,186,1,26,11]))}catch{return!1}},xt=()=>{try{return WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,19,1,17,0,65,1,253,15,65,2,253,15,65,3,253,15,253,147,2,11]))}catch{return!1}},Fe=async e=>{if(Ee)return Promise.resolve();if(ce)throw new Error("multiple calls to 'initializeWebAssembly()' detected.");if(We)throw new Error("previous call to 'initializeWebAssembly()' failed.");ce=!0;let t=e.initTimeout,n=e.numThreads;if(e.simd!==!1){if(e.simd==="relaxed"){if(!xt())throw new Error("Relaxed WebAssembly SIMD is not supported in the current environment.")}else if(!Ut())throw new Error("WebAssembly SIMD is not supported in the current environment.")}let o=_t();n>1&&!o&&(typeof self<"u"&&!self.crossOriginIsolated&&console.warn("env.wasm.numThreads is set to "+n+", but this will not work unless you enable crossOriginIsolated mode. See https://web.dev/cross-origin-isolation-guide/ for more info."),console.warn("WebAssembly multi-threading is not supported in the current environment. Falling back to single-threading."),e.numThreads=n=1);let r=e.wasmPaths,a=typeof r=="string"?r:void 0,s=r?.mjs,i=s?.href??s,c=r?.wasm,l=c?.href??c,f=e.wasmBinary,[d,u]=await ke(i,a,n>1),p=!1,S=[];if(t>0&&S.push(new Promise(O=>{setTimeout(()=>{p=!0,O()},t)})),S.push(new Promise((O,_)=>{let m={numThreads:n};if(f)m.wasmBinary=f;else if(l||a)m.locateFile=g=>l??a+g;else if(i&&i.indexOf("blob:")!==0)m.locateFile=g=>new URL(g,i).href;else if(d){let g=De();g&&(m.locateFile=D=>g+D)}u(m).then(g=>{ce=!1,Ee=!0,he=g,O(),d&&URL.revokeObjectURL(d)},g=>{ce=!1,We=!0,_(g)})})),await Promise.race(S),p)throw new Error(`WebAssembly backend initializing failed due to timeout: ${t}ms`)},y=()=>{if(Ee&&he)return he;throw new Error("WebAssembly is not initialized yet.")}});var B,Q,w,fe=C(()=>{"use strict";J();B=(e,t)=>{let n=y(),o=n.lengthBytesUTF8(e)+1,r=n._malloc(o);return n.stringToUTF8(e,r,o),t.push(r),r},Q=(e,t,n,o)=>{if(typeof e=="object"&&e!==null){if(n.has(e))throw new Error("Circular reference in options");n.add(e)}Object.entries(e).forEach(([r,a])=>{let s=t?t+r:r;if(typeof a=="object")Q(a,s+".",n,o);else if(typeof a=="string"||typeof a=="number")o(s,a.toString());else if(typeof a=="boolean")o(s,a?"1":"0");else throw new Error(`Can't handle extra config type: ${typeof a}`)})},w=e=>{let t=y(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetLastError(r,r+o);let a=Number(t.getValue(r,o===4?"i32":"i64")),s=t.getValue(r+o,"*"),i=s?t.UTF8ToString(s):"";throw new Error(`${e} ERROR_CODE: ${a}, ERROR_MESSAGE: ${i}`)}finally{t.stackRestore(n)}}});var Re,Ne=C(()=>{"use strict";J();fe();Re=e=>{let t=y(),n=0,o=[],r=e||{};try{if(e?.logSeverityLevel===void 0)r.logSeverityLevel=2;else if(typeof e.logSeverityLevel!="number"||!Number.isInteger(e.logSeverityLevel)||e.logSeverityLevel<0||e.logSeverityLevel>4)throw new Error(`log serverity level is not valid: ${e.logSeverityLevel}`);if(e?.logVerbosityLevel===void 0)r.logVerbosityLevel=0;else if(typeof e.logVerbosityLevel!="number"||!Number.isInteger(e.logVerbosityLevel))throw new Error(`log verbosity level is not valid: ${e.logVerbosityLevel}`);e?.terminate===void 0&&(r.terminate=!1);let a=0;return e?.tag!==void 0&&(a=B(e.tag,o)),n=t._OrtCreateRunOptions(r.logSeverityLevel,r.logVerbosityLevel,!!r.terminate,a),n===0&&w("Can't create run options."),e?.extra!==void 0&&Q(e.extra,"",new WeakSet,(s,i)=>{let c=B(s,o),l=B(i,o);t._OrtAddRunConfigEntry(n,c,l)!==0&&w(`Can't set a run config entry: ${s} - ${i}.`)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseRunOptions(n),o.forEach(s=>t._free(s)),a}}});var At,Bt,Mt,le,Ct,$e,Ge=C(()=>{"use strict";J();fe();At=e=>{switch(e){case"disabled":return 0;case"basic":return 1;case"extended":return 2;case"all":return 99;default:throw new Error(`unsupported graph optimization level: ${e}`)}},Bt=e=>{switch(e){case"sequential":return 0;case"parallel":return 1;default:throw new Error(`unsupported execution mode: ${e}`)}},Mt=e=>{e.extra||(e.extra={}),e.extra.session||(e.extra.session={});let t=e.extra.session;t.use_ort_model_bytes_directly||(t.use_ort_model_bytes_directly="1"),e.executionProviders&&e.executionProviders.some(n=>(typeof n=="string"?n:n.name)==="webgpu")&&(e.enableMemPattern=!1)},le=(e,t,n,o)=>{let r=B(t,o),a=B(n,o);y()._OrtAddSessionConfigEntry(e,r,a)!==0&&w(`Can't set a session config entry: ${t} - ${n}.`)},Ct=async(e,t,n)=>{for(let o of t){let r=typeof o=="string"?o:o.name,a=[];switch(r){case"webnn":if(r="WEBNN",typeof o!="string"){let d=o?.deviceType;d&&le(e,"deviceType",d,n)}break;case"webgpu":if(r="JS",typeof o!="string"){let f=o;if(f?.preferredLayout){if(f.preferredLayout!=="NCHW"&&f.preferredLayout!=="NHWC")throw new Error(`preferredLayout must be either 'NCHW' or 'NHWC': ${f.preferredLayout}`);le(e,"preferredLayout",f.preferredLayout,n)}}break;case"wasm":case"cpu":continue;default:throw new Error(`not supported execution provider: ${r}`)}let s=B(r,n),i=a.length,c=0,l=0;if(i>0){c=y()._malloc(i*y().PTR_SIZE),n.push(c),l=y()._malloc(i*y().PTR_SIZE),n.push(l);for(let f=0;f<i;f++)y().setValue(c+f*y().PTR_SIZE,a[f][0],"*"),y().setValue(l+f*y().PTR_SIZE,a[f][1],"*")}await y()._OrtAppendExecutionProvider(e,s,c,l,i)!==0&&w(`Can't append execution provider: ${r}.`)}},$e=async e=>{let t=y(),n=0,o=[],r=e||{};Mt(r);try{let a=At(r.graphOptimizationLevel??"all"),s=Bt(r.executionMode??"sequential"),i=typeof r.logId=="string"?B(r.logId,o):0,c=r.logSeverityLevel??2;if(!Number.isInteger(c)||c<0||c>4)throw new Error(`log serverity level is not valid: ${c}`);let l=r.logVerbosityLevel??0;if(!Number.isInteger(l)||l<0||l>4)throw new Error(`log verbosity level is not valid: ${l}`);let f=typeof r.optimizedModelFilePath=="string"?B(r.optimizedModelFilePath,o):0;if(n=t._OrtCreateSessionOptions(a,!!r.enableCpuMemArena,!!r.enableMemPattern,s,!!r.enableProfiling,0,i,c,l,f),n===0&&w("Can't create session options."),r.executionProviders&&await Ct(n,r.executionProviders,o),r.enableGraphCapture!==void 0){if(typeof r.enableGraphCapture!="boolean")throw new Error(`enableGraphCapture must be a boolean value: ${r.enableGraphCapture}`);le(n,"enableGraphCapture",r.enableGraphCapture.toString(),o)}if(r.freeDimensionOverrides)for(let[d,u]of Object.entries(r.freeDimensionOverrides)){if(typeof d!="string")throw new Error(`free dimension override name must be a string: ${d}`);if(typeof u!="number"||!Number.isInteger(u)||u<0)throw new Error(`free dimension override value must be a non-negative integer: ${u}`);let p=B(d,o);t._OrtAddFreeDimensionOverride(n,p,u)!==0&&w(`Can't set a free dimension override: ${d} - ${u}.`)}return r.extra!==void 0&&Q(r.extra,"",new WeakSet,(d,u)=>{le(n,d,u,o)}),[n,o]}catch(a){throw n!==0&&t._OrtReleaseSessionOptions(n)!==0&&w("Can't release session options."),o.forEach(s=>t._free(s)),a}}});var Y,pe,Z,ze,je,de,me,He,Oe=C(()=>{"use strict";Y=e=>{switch(e){case"int8":return 3;case"uint8":return 2;case"bool":return 9;case"int16":return 5;case"uint16":return 4;case"int32":return 6;case"uint32":return 12;case"float16":return 10;case"float32":return 1;case"float64":return 11;case"string":return 8;case"int64":return 7;case"uint64":return 13;case"int4":return 22;case"uint4":return 21;default:throw new Error(`unsupported data type: ${e}`)}},pe=e=>{switch(e){case 3:return"int8";case 2:return"uint8";case 9:return"bool";case 5:return"int16";case 4:return"uint16";case 6:return"int32";case 12:return"uint32";case 10:return"float16";case 1:return"float32";case 11:return"float64";case 8:return"string";case 7:return"int64";case 13:return"uint64";case 22:return"int4";case 21:return"uint4";default:throw new Error(`unsupported data type: ${e}`)}},Z=(e,t)=>{let n=[-1,4,1,1,2,2,4,8,-1,1,2,8,4,8,-1,-1,-1,-1,-1,-1,-1,.5,.5][e],o=typeof t=="number"?t:t.reduce((r,a)=>r*a,1);return n>0?Math.ceil(o*n):void 0},ze=e=>{switch(e){case"float16":return typeof Float16Array<"u"&&Float16Array.from?Float16Array:Uint16Array;case"float32":return Float32Array;case"uint8":return Uint8Array;case"int8":return Int8Array;case"uint16":return Uint16Array;case"int16":return Int16Array;case"int32":return Int32Array;case"bool":return Uint8Array;case"float64":return Float64Array;case"uint32":return Uint32Array;case"int64":return BigInt64Array;case"uint64":return BigUint64Array;default:throw new Error(`unsupported type: ${e}`)}},je=e=>{switch(e){case"verbose":return 0;case"info":return 1;case"warning":return 2;case"error":return 3;case"fatal":return 4;default:throw new Error(`unsupported logging level: ${e}`)}},de=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",me=e=>e==="float32"||e==="float16"||e==="int32"||e==="int64"||e==="uint32"||e==="uint64"||e==="int8"||e==="uint8"||e==="bool"||e==="uint4"||e==="int4",He=e=>{switch(e){case"none":return 0;case"cpu":return 1;case"cpu-pinned":return 2;case"texture":return 3;case"gpu-buffer":return 4;case"ml-tensor":return 5;default:throw new Error(`unsupported data location: ${e}`)}}});var ee,Te=C(()=>{"use strict";ue();ee=async e=>{if(typeof e=="string")if(N)try{let{readFile:t}=require("node:fs/promises");return new Uint8Array(await t(e))}catch(t){if(t.code==="ERR_FS_FILE_TOO_LARGE"){let{createReadStream:n}=require("node:fs"),o=n(e),r=[];for await(let a of o)r.push(a);return new Uint8Array(Buffer.concat(r))}throw t}else{let t=await fetch(e);if(!t.ok)throw new Error(`failed to load external data file: ${e}`);let n=t.headers.get("Content-Length"),o=n?parseInt(n,10):0;if(o<1073741824)return new Uint8Array(await t.arrayBuffer());{if(!t.body)throw new Error(`failed to load external data file: ${e}, no response body.`);let r=t.body.getReader(),a;try{a=new ArrayBuffer(o)}catch(i){if(i instanceof RangeError){let c=Math.ceil(o/65536);a=new WebAssembly.Memory({initial:c,maximum:c}).buffer}else throw i}let s=0;for(;;){let{done:i,value:c}=await r.read();if(i)break;let l=c.byteLength;new Uint8Array(a,s,l).set(c),s+=l}return new Uint8Array(a,0,o)}}else return e instanceof Blob?new Uint8Array(await e.arrayBuffer()):e instanceof Uint8Array?e:new Uint8Array(e)}});var Dt,Je,Ye,X,kt,Ve,ve,Ze,Xe,qe,Ke,Qe,et=C(()=>{"use strict";Ne();Ge();Oe();J();fe();Te();Dt=(e,t)=>{y()._OrtInit(e,t)!==0&&w("Can't initialize onnxruntime.")},Je=async e=>{Dt(e.wasm.numThreads,je(e.logLevel))},Ye=async(e,t)=>{y().asyncInit?.()},X=new Map,kt=e=>{let t=y(),n=t.stackSave();try{let o=t.PTR_SIZE,r=t.stackAlloc(2*o);t._OrtGetInputOutputCount(e,r,r+o)!==0&&w("Can't get session input/output count.");let s=o===4?"i32":"i64";return[Number(t.getValue(r,s)),Number(t.getValue(r+o,s))]}finally{t.stackRestore(n)}},Ve=(e,t)=>{let n=y(),o=n.stackSave(),r=0;try{let a=n.PTR_SIZE,s=n.stackAlloc(2*a);n._OrtGetInputOutputMetadata(e,t,s,s+a)!==0&&w("Can't get session input/output metadata.");let c=Number(n.getValue(s,"*"));r=Number(n.getValue(s+a,"*"));let l=n.HEAP32[r/4];if(l===0)return[c,0];let f=n.HEAPU32[r/4+1],d=[];for(let u=0;u<f;u++){let p=Number(n.getValue(r+8+u*a,"*"));d.push(p!==0?n.UTF8ToString(p):Number(n.getValue(r+8+(u+f)*a,"*")))}return[c,l,d]}finally{n.stackRestore(o),r!==0&&n._OrtFree(r)}},ve=e=>{let t=y(),n=t._malloc(e.byteLength);if(n===0)throw new Error(`Can't create a session. failed to allocate a buffer of size ${e.byteLength}.`);return t.HEAPU8.set(e,n),[n,e.byteLength]},Ze=async(e,t)=>{let n,o,r=y();Array.isArray(e)?[n,o]=e:e.buffer===r.HEAPU8.buffer?[n,o]=[e.byteOffset,e.byteLength]:[n,o]=ve(e);let a=0,s=0,i=0,c=[],l=[],f=[];try{if([s,c]=await $e(t),t?.externalData&&r.mountExternalData){let h=[];for(let E of t.externalData){let v=typeof E=="string"?E:E.path;h.push(ee(typeof E=="string"?E:E.data).then(U=>{r.mountExternalData(v,U)}))}await Promise.all(h)}for(let h of t?.executionProviders??[])if((typeof h=="string"?h:h.name)==="webnn"){if(r.shouldTransferToMLTensor=!1,typeof h!="string"){let v=h,U=v?.context,x=v?.gpuDevice,j=v?.deviceType,re=v?.powerPreference;U?r.currentContext=U:x?r.currentContext=await r.webnnCreateMLContext(x):r.currentContext=await r.webnnCreateMLContext({deviceType:j,powerPreference:re})}else r.currentContext=await r.webnnCreateMLContext();break}a=await r._OrtCreateSession(n,o,s),r.webgpuOnCreateSession?.(a),a===0&&w("Can't create a session."),r.jsepOnCreateSession?.(),r.currentContext&&(r.webnnRegisterMLContext(a,r.currentContext),r.currentContext=void 0,r.shouldTransferToMLTensor=!0);let[d,u]=kt(a),p=!!t?.enableGraphCapture,S=[],O=[],_=[],m=[],g=[];for(let h=0;h<d;h++){let[E,v,U]=Ve(a,h);E===0&&w("Can't get an input name."),l.push(E);let x=r.UTF8ToString(E);S.push(x),_.push(v===0?{name:x,isTensor:!1}:{name:x,isTensor:!0,type:pe(v),shape:U})}for(let h=0;h<u;h++){let[E,v,U]=Ve(a,h+d);E===0&&w("Can't get an output name."),f.push(E);let x=r.UTF8ToString(E);O.push(x),m.push(v===0?{name:x,isTensor:!1}:{name:x,isTensor:!0,type:pe(v),shape:U})}return X.set(a,[a,l,f,null,p,!1]),[a,S,O,_,m]}catch(d){throw l.forEach(u=>r._OrtFree(u)),f.forEach(u=>r._OrtFree(u)),i!==0&&r._OrtReleaseBinding(i)!==0&&w("Can't release IO binding."),a!==0&&r._OrtReleaseSession(a)!==0&&w("Can't release session."),d}finally{r._free(n),s!==0&&r._OrtReleaseSessionOptions(s)!==0&&w("Can't release session options."),c.forEach(d=>r._free(d)),r.unmountExternalData?.()}},Xe=e=>{let t=y(),n=X.get(e);if(!n)throw new Error(`cannot release session. invalid session id: ${e}`);let[o,r,a,s,i]=n;s&&(i&&t._OrtClearBoundOutputs(s.handle)!==0&&w("Can't clear bound outputs."),t._OrtReleaseBinding(s.handle)!==0&&w("Can't release IO binding.")),t.jsepOnReleaseSession?.(e),t.webnnOnReleaseSession?.(e),t.webgpuOnReleaseSession?.(e),r.forEach(c=>t._OrtFree(c)),a.forEach(c=>t._OrtFree(c)),t._OrtReleaseSession(o)!==0&&w("Can't release session."),X.delete(e)},qe=async(e,t,n,o,r,a,s=!1)=>{if(!e){t.push(0);return}let i=y(),c=i.PTR_SIZE,l=e[0],f=e[1],d=e[3],u=d,p,S;if(l==="string"&&(d==="gpu-buffer"||d==="ml-tensor"))throw new Error("String tensor is not supported on GPU.");if(s&&d!=="gpu-buffer")throw new Error(`External buffer must be provided for input/output index ${a} when enableGraphCapture is true.`);if(d==="gpu-buffer"){let m=e[2].gpuBuffer;S=Z(Y(l),f);{let g=i.jsepRegisterBuffer;if(!g)throw new Error('Tensor location "gpu-buffer" is not supported without using WebGPU.');p=g(o,a,m,S)}}else if(d==="ml-tensor"){let m=e[2].mlTensor;S=Z(Y(l),f);let g=i.webnnRegisterMLTensor;if(!g)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');p=g(o,m,Y(l),f)}else{let m=e[2];if(Array.isArray(m)){S=c*m.length,p=i._malloc(S),n.push(p);for(let g=0;g<m.length;g++){if(typeof m[g]!="string")throw new TypeError(`tensor data at index ${g} is not a string`);i.setValue(p+g*c,B(m[g],n),"*")}}else{let g=i.webnnIsGraphInput;if(l!=="string"&&g){let D=i.UTF8ToString(r);if(g(o,D)){let h=Y(l);S=Z(h,f),u="ml-tensor";let E=i.webnnCreateTemporaryTensor,v=i.webnnUploadTensor;if(!E||!v)throw new Error('Tensor location "ml-tensor" is not supported without using WebNN.');let U=await E(o,h,f);v(U,new Uint8Array(m.buffer,m.byteOffset,m.byteLength)),p=U}else S=m.byteLength,p=i._malloc(S),n.push(p),i.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,S),p)}else S=m.byteLength,p=i._malloc(S),n.push(p),i.HEAPU8.set(new Uint8Array(m.buffer,m.byteOffset,S),p)}}let O=i.stackSave(),_=i.stackAlloc(4*f.length);try{f.forEach((g,D)=>i.setValue(_+D*c,g,c===4?"i32":"i64"));let m=i._OrtCreateTensor(Y(l),p,S,_,f.length,He(u));m===0&&w(`Can't create tensor for input/output. session=${o}, index=${a}.`),t.push(m)}finally{i.stackRestore(O)}},Ke=async(e,t,n,o,r,a)=>{let s=y(),i=s.PTR_SIZE,c=X.get(e);if(!c)throw new Error(`cannot run inference. invalid session id: ${e}`);let l=c[0],f=c[1],d=c[2],u=c[3],p=c[4],S=c[5],O=t.length,_=o.length,m=0,g=[],D=[],h=[],E=[],v=s.stackSave(),U=s.stackAlloc(O*i),x=s.stackAlloc(O*i),j=s.stackAlloc(_*i),re=s.stackAlloc(_*i);try{[m,g]=Re(a);for(let b=0;b<O;b++)await qe(n[b],D,E,e,f[t[b]],t[b],p);for(let b=0;b<_;b++)await qe(r[b],h,E,e,d[o[b]],O+o[b],p);for(let b=0;b<O;b++)s.setValue(U+b*i,D[b],"*"),s.setValue(x+b*i,f[t[b]],"*");for(let b=0;b<_;b++)s.setValue(j+b*i,h[b],"*"),s.setValue(re+b*i,d[o[b]],"*");s.jsepOnRunStart?.(l),s.webnnOnRunStart?.(l);let M;M=await s._OrtRun(l,x,U,O,re,_,j,m),M!==0&&w("failed to call OrtRun().");let H=[];for(let b=0;b<_;b++){let V=Number(s.getValue(j+b*i,"*"));if(V===h[b]){H.push(r[b]);continue}let Le=s.stackSave(),k=s.stackAlloc(4*i),K=!1,P,A=0;try{s._OrtGetTensorData(V,k,k+i,k+2*i,k+3*i)!==0&&w(`Can't access output tensor data on index ${b}.`);let ye=i===4?"i32":"i64",ne=Number(s.getValue(k,ye));A=s.getValue(k+i,"*");let _e=s.getValue(k+i*2,"*"),bt=Number(s.getValue(k+i*3,ye)),G=[];for(let I=0;I<bt;I++)G.push(Number(s.getValue(_e+I*i,ye)));s._OrtFree(_e)!==0&&w("Can't free memory for tensor dims.");let z=G.reduce((I,T)=>I*T,1);P=pe(ne);let oe=u?.outputPreferredLocations[o[b]];if(P==="string"){if(oe==="gpu-buffer"||oe==="ml-tensor")throw new Error("String tensor is not supported on GPU.");let I=[];for(let T=0;T<z;T++){let q=s.getValue(A+T*i,"*"),se=s.getValue(A+(T+1)*i,"*"),Ue=T===z-1?void 0:se-q;I.push(s.UTF8ToString(q,Ue))}H.push([P,G,I,"cpu"])}else if(oe==="gpu-buffer"&&z>0){let I=s.jsepGetBuffer;if(!I)throw new Error('preferredLocation "gpu-buffer" is not supported without using WebGPU.');let T=I(A),q=Z(ne,z);if(q===void 0||!de(P))throw new Error(`Unsupported data type: ${P}`);K=!0,H.push([P,G,{gpuBuffer:T,download:s.jsepCreateDownloader(T,q,P),dispose:()=>{s._OrtReleaseTensor(V)!==0&&w("Can't release tensor.")}},"gpu-buffer"])}else if(oe==="ml-tensor"&&z>0){let I=s.webnnEnsureTensor,T=s.webnnIsInt64Supported;if(!I||!T)throw new Error('preferredLocation "ml-tensor" is not supported without using WebNN.');if(Z(ne,z)===void 0||!me(P))throw new Error(`Unsupported data type: ${P}`);if(P==="int64"&&!T(e))throw new Error('preferredLocation "ml-tensor" for int64 output is not supported by current WebNN Context.');let se=await I(e,A,ne,G,!1);K=!0,H.push([P,G,{mlTensor:se,download:s.webnnCreateMLTensorDownloader(A,P),dispose:()=>{s.webnnReleaseTensorId(A),s._OrtReleaseTensor(V)}},"ml-tensor"])}else{let I=ze(P),T=new I(z);new Uint8Array(T.buffer,T.byteOffset,T.byteLength).set(s.HEAPU8.subarray(A,A+T.byteLength)),H.push([P,G,T,"cpu"])}}finally{s.stackRestore(Le),P==="string"&&A&&s._free(A),K||s._OrtReleaseTensor(V),s.webnnOnRunEnd?.(l)}}return u&&!p&&(s._OrtClearBoundOutputs(u.handle)!==0&&w("Can't clear bound outputs."),X.set(e,[l,f,d,u,p,!1])),H}finally{s.stackRestore(v),D.forEach(M=>s._OrtReleaseTensor(M)),h.forEach(M=>s._OrtReleaseTensor(M)),E.forEach(M=>s._free(M)),m!==0&&s._OrtReleaseRunOptions(m),g.forEach(M=>s._free(M))}},Qe=e=>{let t=y(),n=X.get(e);if(!n)throw new Error("invalid session id");let o=n[0],r=t._OrtEndProfiling(o);r===0&&w("Can't get an profile file name."),t._OrtFree(r)}});var be,Pe,tt,rt,nt,ot,st,at,it,ut,ct,Ie=C(()=>{"use strict";be=require("onnxruntime-common");et();J();Se();Pe=!1,tt=!1,rt=!1,nt=async()=>{if(!tt){if(Pe)throw new Error("multiple calls to 'initWasm()' detected.");if(rt)throw new Error("previous call to 'initWasm()' failed.");Pe=!0;try{await Fe(be.env.wasm),await Je(be.env),tt=!0}catch(e){throw rt=!0,e}finally{Pe=!1}}},ot=async e=>{await Ye(be.env,e)},st=async e=>ve(e),at=async(e,t)=>Ze(e,t),it=async e=>{Xe(e)},ut=async(e,t,n,o,r,a)=>Ke(e,t,n,o,r,a),ct=async e=>{Qe(e)}});var W,ft,Ft,ge,lt=C(()=>{"use strict";W=require("onnxruntime-common");Ie();Oe();ue();Te();ft=(e,t)=>{switch(e.location){case"cpu":return[e.type,e.dims,e.data,"cpu"];case"gpu-buffer":return[e.type,e.dims,{gpuBuffer:e.gpuBuffer},"gpu-buffer"];case"ml-tensor":return[e.type,e.dims,{mlTensor:e.mlTensor},"ml-tensor"];default:throw new Error(`invalid data location: ${e.location} for ${t()}`)}},Ft=e=>{switch(e[3]){case"cpu":return new W.Tensor(e[0],e[2],e[1]);case"gpu-buffer":{let t=e[0];if(!de(t))throw new Error(`not supported data type: ${t} for deserializing GPU tensor`);let{gpuBuffer:n,download:o,dispose:r}=e[2];return W.Tensor.fromGpuBuffer(n,{dataType:t,dims:e[1],download:o,dispose:r})}case"ml-tensor":{let t=e[0];if(!me(t))throw new Error(`not supported data type: ${t} for deserializing MLTensor tensor`);let{mlTensor:n,download:o,dispose:r}=e[2];return W.Tensor.fromMLTensor(n,{dataType:t,dims:e[1],download:o,dispose:r})}default:throw new Error(`invalid data location: ${e[3]}`)}},ge=class{async fetchModelAndCopyToWasmMemory(t){return st(await ee(t))}async loadModel(t,n){(0,W.TRACE_FUNC_BEGIN)();let o;typeof t=="string"?N?o=await ee(t):o=await this.fetchModelAndCopyToWasmMemory(t):o=t,[this.sessionId,this.inputNames,this.outputNames,this.inputMetadata,this.outputMetadata]=await at(o,n),(0,W.TRACE_FUNC_END)()}async dispose(){return it(this.sessionId)}async run(t,n,o){(0,W.TRACE_FUNC_BEGIN)();let r=[],a=[];Object.entries(t).forEach(u=>{let p=u[0],S=u[1],O=this.inputNames.indexOf(p);if(O===-1)throw new Error(`invalid input '${p}'`);r.push(S),a.push(O)});let s=[],i=[];Object.entries(n).forEach(u=>{let p=u[0],S=u[1],O=this.outputNames.indexOf(p);if(O===-1)throw new Error(`invalid output '${p}'`);s.push(S),i.push(O)});let c=r.map((u,p)=>ft(u,()=>`input "${this.inputNames[a[p]]}"`)),l=s.map((u,p)=>u?ft(u,()=>`output "${this.outputNames[i[p]]}"`):null),f=await ut(this.sessionId,a,c,i,l,o),d={};for(let u=0;u<f.length;u++)d[this.outputNames[i[u]]]=s[u]??Ft(f[u]);return(0,W.TRACE_FUNC_END)(),d}startProfiling(){}endProfiling(){ct(this.sessionId)}}});var dt={};xe(dt,{OnnxruntimeWebAssemblyBackend:()=>we,initializeFlags:()=>pt,wasmBackend:()=>Rt});var L,pt,we,Rt,mt=C(()=>{"use strict";L=require("onnxruntime-common");Ie();lt();pt=()=>{(typeof L.env.wasm.initTimeout!="number"||L.env.wasm.initTimeout<0)&&(L.env.wasm.initTimeout=0);let e=L.env.wasm.simd;if(typeof e!="boolean"&&e!==void 0&&e!=="fixed"&&e!=="relaxed"&&(console.warn(`Property "env.wasm.simd" is set to unknown value "${e}". Reset it to \`false\` and ignore SIMD feature checking.`),L.env.wasm.simd=!1),typeof L.env.wasm.proxy!="boolean"&&(L.env.wasm.proxy=!1),typeof L.env.wasm.trace!="boolean"&&(L.env.wasm.trace=!1),typeof L.env.wasm.numThreads!="number"||!Number.isInteger(L.env.wasm.numThreads)||L.env.wasm.numThreads<=0)if(typeof self<"u"&&!self.crossOriginIsolated)L.env.wasm.numThreads=1;else{let t=typeof navigator>"u"?require("node:os").cpus().length:navigator.hardwareConcurrency;L.env.wasm.numThreads=Math.min(4,Math.ceil((t||1)/2))}},we=class{async init(t){pt(),await nt(),await ot(t)}async createInferenceSessionHandler(t,n){let o=new ge;return await o.loadModel(t,n),o}},Rt=new we});var F={};xe(F,{default:()=>$t});module.exports=Ae(F);R(F,require("onnxruntime-common"),module.exports);var Nt=Et(require("onnxruntime-common")),te=require("onnxruntime-common");var Be="1.22.0-dev.20250409-89f8206ba4";var $t=Nt;{let e=(mt(),Ae(dt)).wasmBackend;(0,te.registerBackend)("cpu",e,10),(0,te.registerBackend)("wasm",e,10)}Object.defineProperty(te.env.versions,"web",{value:Be,enumerable:!0});0&&(module.exports={...require("onnxruntime-common")});
//# sourceMappingURL=ort.node.min.js.map
