{"license": "MIT", "unpkg": "dist/ort.min.js", "name": "onnxruntime-web", "repository": {"url": "https://github.com/Microsoft/onnxruntime.git", "type": "git"}, "author": "fs-eire", "version": "1.22.0-dev.20250409-89f8206ba4", "jsdelivr": "dist/ort.min.js", "dependencies": {"flatbuffers": "^25.1.24", "guid-typescript": "^1.0.9", "long": "^5.2.3", "onnxruntime-common": "1.22.0-dev.20250409-89f8206ba4", "platform": "^1.3.6", "protobufjs": "^7.2.4"}, "scripts": {"preprepare": "node -e \"require('node:fs').copyFileSync('./node_modules/long/index.d.ts', './node_modules/long/umd/index.d.ts')\"", "prepare": "tsc --build ./script", "build:doc": "node ./script/generate-webgl-operator-md && node ./script/generate-webgpu-operator-md", "pull:wasm": "node ./script/pull-prebuilt-wasm-artifacts", "test:e2e": "node ./test/e2e/run", "prebuild": "tsc -p . --noEmit && tsc -p lib/wasm/proxy-worker --noEmit", "build": "node ./script/build", "test": "tsc --build ../scripts && node ../scripts/prepare-onnx-node-tests && node ./script/test-runner-cli", "prepack": "node ./script/build && node ./script/prepack"}, "keywords": ["ONNX", "ONNXRuntime", "ONNX Runtime"], "devDependencies": {"@chiragrupani/karma-chromium-edge-launcher": "^2.2.2", "@petamoriken/float16": "^3.8.7", "@types/chai": "^4.3.4", "@types/emscripten": "^1.39.6", "@types/karma": "^6.1.0", "@types/minimatch": "^5.1.2", "@types/minimist": "^1.2.2", "@types/platform": "^1.3.4", "@webgpu/types": "^0.1.42", "base64-js": "^1.5.1", "chai": "^4.3.7", "electron": "^28.1.4", "globby": "^13.1.3", "karma": "^6.4.1", "karma-browserstack-launcher": "^1.6.0", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.1", "karma-edge-launcher": "^0.4.2", "karma-electron": "^7.3.0", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-safari-applescript-launcher": "^0.1.1", "karma-sourcemap-loader": "^0.4.0", "minimatch": "^7.4.2", "minimist": "^1.2.8", "numpy-parser": "^1.2.3", "source-map": "^0.7.4", "strip-json-comments": "^5.0.0"}, "main": "dist/ort.node.min.js", "browser": "dist/ort.min.js", "exports": {".": {"node": {"import": "./dist/ort.node.min.mjs", "require": "./dist/ort.node.min.js"}, "import": {"onnxruntime-web-use-extern-wasm": "./dist/ort.min.mjs", "default": "./dist/ort.bundle.min.mjs"}, "require": "./dist/ort.min.js"}, "./all": {"import": {"onnxruntime-web-use-extern-wasm": "./dist/ort.all.min.mjs", "default": "./dist/ort.all.bundle.min.mjs"}, "require": "./dist/ort.all.min.js"}, "./wasm": {"import": {"onnxruntime-web-use-extern-wasm": "./dist/ort.wasm.min.mjs", "default": "./dist/ort.wasm.bundle.min.mjs"}, "require": "./dist/ort.wasm.min.js"}, "./webgl": {"import": "./dist/ort.webgl.min.mjs", "require": "./dist/ort.webgl.min.js"}, "./webgpu": {"import": {"onnxruntime-web-use-extern-wasm": "./dist/ort.webgpu.min.mjs", "default": "./dist/ort.webgpu.bundle.min.mjs"}, "require": "./dist/ort.webgpu.min.js"}}, "types": "./types.d.ts", "description": "A Javascript library for running ONNX models on browsers"}