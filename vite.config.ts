import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  server: {
    headers: {
      'Cross-Origin-Embedder-Policy': 'require-corp',
      'Cross-Origin-Opener-Policy': 'same-origin',
    },
    fs: {
      allow: ['..']
    }
  },
  optimizeDeps: {
    exclude: ['@huggingface/transformers'],
    include: ['onnxruntime-web']
  },
  worker: {
    format: 'es'
  },
  build: {
    target: 'esnext',
    rollupOptions: {
      output: {
        manualChunks: {
          'transformers': ['@huggingface/transformers'],
          'react-vendor': ['react', 'react-dom'],
          'ui-vendor': ['reactflow', 'framer-motion'],
        },
      },
      external: (id) => {
        // Don't bundle WebGPU-related modules
        if (id.includes('onnxruntime-web/webgpu')) return true
        return false
      }
    },
  },
  define: {
    global: 'globalThis',
  },
})
