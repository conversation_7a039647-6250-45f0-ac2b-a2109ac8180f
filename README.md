# Local LLM Web Agent

A powerful web application that performs LLM inference using WebGPU, featuring an intelligent agent that can search the web and analyze content with an interactive whiteboard interface.

## 🚀 Features

### Core Functionality
- **WebGPU-Powered LLM**: Runs Phi-3 Mini (3.8B parameters) locally in your browser using WebGPU for optimal performance
- **Intelligent Agent**: Autonomous tool usage for web search and content analysis
- **Interactive Whiteboard**: Visual representation of sources, queries, and notes with drag-and-drop functionality
- **Real-time Chat**: Responsive chat interface with typing indicators and status updates

### Agent Capabilities
- **Web Search**: Integrated Google Custom Search API for finding relevant information
- **Content Fetching**: Retrieves and analyzes web page content
- **Context Maintenance**: Maintains conversation context across multiple tool calls
- **Source Attribution**: Proper attribution and links back to original sources

### User Interface
- **Whiteboard View**: Thumbnail previews and sticky notes for referenced sources
- **Responsive Design**: Works on desktop and mobile devices
- **Visual Indicators**: Clear status indicators for agent activities (searching, fetching, processing)
- **Interactive Elements**: Pan, zoom, and rearrange whiteboard elements

## 🛠️ Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **LLM**: Microsoft Phi-3 Mini via Hugging Face Transformers.js
- **WebGPU**: Direct browser-based inference (fallback to WASM)
- **UI Framework**: Tailwind CSS + Framer Motion
- **Whiteboard**: React Flow for interactive node-based interface
- **Search API**: Google Custom Search API

## 📋 Prerequisites

- **Browser**: Chrome 113+ or Edge 113+ (for WebGPU support)
- **Node.js**: 18.0.0 or higher
- **API Keys**: Google Custom Search API key and Search Engine ID

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd local_llm_web
npm install
```

### 2. Environment Setup

Copy the environment template and add your API keys:

```bash
cp .env.example .env
```

Edit `.env` and add your credentials:

```env
VITE_GOOGLE_API_KEY=your_google_api_key_here
VITE_GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here
```

### 3. Get API Credentials

#### Google Custom Search API:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Custom Search API"
4. Create credentials (API Key)
5. Go to [Google Custom Search Engine](https://cse.google.com/cse/)
6. Create a new search engine and get the Search Engine ID

### 4. Run the Application

```bash
npm run dev
```

Open [http://localhost:5173](http://localhost:5173) in your browser.

## 🎯 Usage

### Basic Usage
1. **Start a Conversation**: Type your question in the chat interface
2. **Watch the Agent Work**: See real-time status as the agent searches and fetches content
3. **Explore Sources**: View referenced sources on the interactive whiteboard
4. **Add Notes**: Create and edit notes to organize your research

### Advanced Features
- **Connect Sources**: Draw connections between related sources on the whiteboard
- **Edit Notes**: Double-click notes to edit content inline
- **Pan and Zoom**: Navigate large research spaces easily
- **Source Details**: Click on sources to view detailed information

## 🏗️ Project Structure

```
src/
├── components/           # React components
│   ├── nodes/           # Custom whiteboard nodes
│   ├── ChatInterface.tsx
│   ├── WhiteboardInterface.tsx
│   └── ...
├── contexts/            # React contexts
│   ├── LLMContext.tsx
│   └── AgentContext.tsx
├── services/            # Core services
│   ├── llm.ts          # LLM inference service
│   ├── agent.ts        # Agent orchestration
│   └── tools.ts        # Tool implementations
├── types/              # TypeScript definitions
├── utils/              # Utility functions
└── ...
```

## 🔧 Configuration

### LLM Configuration
The LLM can be configured in `src/contexts/LLMContext.tsx`:

```typescript
const defaultConfig: LLMConfig = {
  modelId: 'microsoft/Phi-3-mini-4k-instruct-onnx-web',
  maxTokens: 512,
  temperature: 0.7,
  topP: 0.9,
  device: 'webgpu' // or 'wasm'
}
```

### Tool Configuration
Add new tools in `src/services/tools.ts` following the Tool interface.

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with UI
npm run test:ui

# Run linting
npm run lint
```

## 🚀 Building for Production

```bash
npm run build
npm run preview
```

## 🔍 Troubleshooting

### WebGPU Issues
- **Not Supported**: Use Chrome 113+ or Edge 113+
- **Performance Issues**: Check if hardware acceleration is enabled
- **Fallback to WASM**: The app automatically falls back if WebGPU is unavailable

### API Issues
- **Search Errors**: Verify your Google API key and Search Engine ID
- **CORS Issues**: The app uses a CORS proxy for web page fetching
- **Rate Limits**: Google Custom Search has daily quotas

### Performance
- **Slow Loading**: First model load takes time; subsequent uses are faster
- **Memory Usage**: WebGPU models use significant GPU memory
- **Browser Compatibility**: Some features require modern browser APIs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Hugging Face**: For Transformers.js and model hosting
- **Microsoft**: For the Phi-3 model
- **React Flow**: For the whiteboard interface
- **Google**: For the Custom Search API

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Search existing GitHub issues
3. Create a new issue with detailed information

---

**Note**: This application runs AI models locally in your browser. The first load may take some time as models are downloaded and cached.
